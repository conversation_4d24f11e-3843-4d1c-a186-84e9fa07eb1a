#!/usr/bin/env python3
"""
AI/NLP Analysis: <PERSON>TWB Foundation Portfolio Alignment Assessment
Analyzes how well the current portfolio optimizer aligns with the case study requirements
"""

import re
from collections import Counter
import pandas as pd

class MTWBAlignmentAnalyzer:
    """AI/NLP analyzer for assessing portfolio alignment with MTWB foundation goals"""
    
    def __init__(self):
        # Key themes from <PERSON> case study
        self.case_study_themes = {
            'community_impact': [
                'community', 'neighborhood', 'public spaces', 'revitalization', 
                'local', 'grassroots', 'engagement', 'stewardship', 'partnerships',
                'philadelphia', 'urban renewal', 'civic', 'gathering', 'inclusive'
            ],
            'sustainability': [
                'sustainability', 'sustainable', 'lasting', 'enduring', 'long-term',
                'environmental', 'ESG', 'responsible', 'ethical', 'green', 'clean'
            ],
            'social_good': [
                'philanthropist', 'foundation', 'nonprofit', 'social', 'inspire',
                'better world', 'make difference', 'transformative', 'pride',
                'safe', 'welcoming', 'accessible', 'cultural expression'
            ],
            'financial_goals': [
                'growth', 'capital', 'investment', 'return', 'fund', 'drawdown',
                'portfolio', 'diversified', 'risk management', 'allocation'
            ],
            'infrastructure_focus': [
                'infrastructure', 'facilities', 'modernized', 'upgraded', 'lighting',
                'courts', 'fields', 'centers', 'parks', 'recreation', 'sports'
            ]
        }
        
        # Current portfolio characteristics
        self.current_portfolio_analysis = {
            'esg_alignment': 0,
            'community_focus': 0,
            'sustainability_score': 0,
            'risk_appropriateness': 0,
            'goal_alignment': 0
        }
    
    def analyze_case_study_requirements(self):
        """Extract and analyze key requirements from Connor Barwin case study"""
        
        case_study_text = """
        Connor Barwin (WG'23) believes in the power of community. The former NFL linebacker 
        established the Make the World Better (MTWB) foundation in Philadelphia dedicated to 
        revitalizing public spaces and strengthening communities by building inclusive, safe, 
        and inspiring places for people to gather, play, and thrive. MTWB works alongside 
        local neighborhoods, ensuring that each project reflects the needs and dreams of the 
        community it serves. Connor would like to grow his initial MTWB investment of $500,000 
        into a fund of at least $1.5 million over the next 10 years, supporting capital 
        projects that revitalize public spaces and inspire long-lasting community pride. 
        Starting the end of year three (2029), he will make an annual drawdown from the fund 
        of $10,000 to support grassroots community programs. By 2036, MTWB's fund will be 
        ready to invest in a truly transformative capital project designed to leave a lasting, 
        visible legacy for Philadelphia's neighborhoods. Every dollar invested reflects MTWB's 
        values — community-first, locally rooted, and sustainability-minded.
        """
        
        # Simple sentiment analysis (positive words count)
        positive_words = ['better', 'inspiring', 'safe', 'inclusive', 'pride', 'transformative', 'lasting', 'vibrant']
        sentiment_score = sum(case_study_text.lower().count(word) for word in positive_words) / len(positive_words)
        
        # Extract key financial metrics
        financial_requirements = {
            'initial_capital': 500000,
            'target_capital': 1500000,
            'time_horizon': 10,
            'annual_drawdown': 10000,
            'drawdown_start_year': 3,
            'required_growth_multiple': 3.0,  # 1.5M / 0.5M
            'required_annual_return': 0.116  # Approximately 11.6% to reach target with drawdowns
        }
        
        # Theme frequency analysis
        theme_scores = {}
        for theme, keywords in self.case_study_themes.items():
            score = sum(case_study_text.lower().count(keyword) for keyword in keywords)
            theme_scores[theme] = score
        
        return {
            'sentiment_score': sentiment_score,
            'financial_requirements': financial_requirements,
            'theme_scores': theme_scores,
            'key_values': ['community-first', 'locally rooted', 'sustainability-minded']
        }
    
    def analyze_current_portfolio_stocks(self):
        """Analyze current stock selection for ESG and community alignment"""
        
        # Current growth-focused categories from the optimizer
        current_categories = {
            "ESG-Tech-Growth": [
                "MSFT", "GOOGL", "AAPL", "NVDA", "AMD", "CRM", "ADBE", "NOW",
                "TSLA", "NFLX", "META", "AMZN", "SHOP", "SQ", "PYPL", "V"
            ],
            "Clean-Energy-Infrastructure": [
                "ICLN", "PBW", "QCLN", "CNRG", "NEE", "ENPH", "SEDG", "FSLR",
                "BEP", "AES", "XEL", "SO", "DUK", "EXC", "AWK", "WEC"
            ],
            "Healthcare-Innovation": [
                "JNJ", "UNH", "PFE", "ABBV", "LLY", "TMO", "DHR", "ABT",
                "ISRG", "DXCM", "VEEV", "ILMN", "REGN", "GILD", "BIIB", "MRNA"
            ],
            "Education-Community": [
                "CHGG", "LRND", "STRA", "APEI", "CECO", "UTI", "LINC", "PRDO",
                "EDUC", "COUR", "2U", "BOXL", "PCAR", "NABL", "AFYA", "TAL"
            ],
            "Sustainable-Consumer": [
                "COST", "TGT", "WMT", "HD", "LOW", "NKE", "SBUX", "MCD",
                "PG", "UL", "KO", "PEP", "NSRGY", "UN", "CL", "EL"
            ],
            "Financial-Inclusion": [
                "JPM", "BAC", "WFC", "C", "GS", "MS", "BLK", "SCHW",
                "AXP", "V", "MA", "PYPL", "SQ", "AFRM", "UPST", "LC"
            ]
        }
        
        # ESG scoring for each category
        esg_scores = {
            "ESG-Tech-Growth": 8,  # High ESG focus, innovation
            "Clean-Energy-Infrastructure": 10,  # Perfect alignment with sustainability
            "Healthcare-Innovation": 7,  # Social good, but mixed ESG
            "Education-Community": 9,  # Strong community impact
            "Sustainable-Consumer": 6,  # Mixed sustainability practices
            "Financial-Inclusion": 5   # Traditional finance, limited ESG focus
        }
        
        # Community impact scoring
        community_scores = {
            "ESG-Tech-Growth": 6,  # Indirect community impact
            "Clean-Energy-Infrastructure": 8,  # Infrastructure benefits communities
            "Healthcare-Innovation": 9,  # Direct health benefits to communities
            "Education-Community": 10,  # Direct community and education impact
            "Sustainable-Consumer": 5,  # Limited direct community impact
            "Financial-Inclusion": 4   # Limited community focus
        }
        
        return {
            'categories': current_categories,
            'esg_scores': esg_scores,
            'community_scores': community_scores,
            'total_stocks': sum(len(stocks) for stocks in current_categories.values())
        }
    
    def calculate_alignment_score(self):
        """Calculate overall alignment score with MTWB goals"""
        
        case_analysis = self.analyze_case_study_requirements()
        portfolio_analysis = self.analyze_current_portfolio_stocks()
        
        # Weight different alignment factors
        weights = {
            'esg_alignment': 0.25,
            'community_focus': 0.30,
            'sustainability': 0.20,
            'financial_viability': 0.15,
            'innovation_impact': 0.10
        }
        
        # Calculate scores (0-10 scale)
        scores = {
            'esg_alignment': sum(portfolio_analysis['esg_scores'].values()) / len(portfolio_analysis['esg_scores']),
            'community_focus': sum(portfolio_analysis['community_scores'].values()) / len(portfolio_analysis['community_scores']),
            'sustainability': 8.5,  # Strong clean energy focus
            'financial_viability': 7.0,  # Need to verify if returns meet 11.6% target
            'innovation_impact': 8.0   # Good tech and healthcare innovation
        }
        
        # Calculate weighted alignment score
        alignment_score = sum(scores[factor] * weights[factor] for factor in weights.keys())
        
        return {
            'overall_score': alignment_score,
            'individual_scores': scores,
            'weights': weights,
            'grade': self.get_alignment_grade(alignment_score)
        }
    
    def get_alignment_grade(self, score):
        """Convert numerical score to letter grade"""
        if score >= 9.0:
            return "A+ (Excellent Alignment)"
        elif score >= 8.0:
            return "A (Strong Alignment)"
        elif score >= 7.0:
            return "B+ (Good Alignment)"
        elif score >= 6.0:
            return "B (Moderate Alignment)"
        elif score >= 5.0:
            return "C (Weak Alignment)"
        else:
            return "D (Poor Alignment)"
    
    def generate_recommendations(self):
        """Generate AI-powered recommendations for better alignment"""
        
        recommendations = {
            'add_stocks': [
                # Community Development Financial Institutions (CDFIs)
                "HOPE", "SELF", "CDFI",  # If publicly traded
                
                # Infrastructure & Urban Development
                "AMT", "CCI", "SBAC",  # Cell tower REITs for digital infrastructure
                "AWK", "WTR", "CWCO",  # Water utilities for community infrastructure
                
                # ESG-focused ETFs
                "ESGU", "ESGD", "SUSB", "SUSC",  # ESG broad market
                "ICLN", "QCLN", "PBW",  # Clean energy (already included)
                "KRMA", "VOTE", "VSGX",  # ESG governance focus
                
                # Community Impact
                "PLD", "EXR", "PSA",  # REITs that support community development
                "HD", "LOW",  # Home improvement (community building)
                "COST", "WMT",  # Community retail (already included)
            ],
            
            'remove_stocks': [
                # Reduce exposure to pure financial speculation
                "COIN", "MSTR", "RIOT", "MARA",  # Crypto exposure
                
                # Reduce traditional big banks (keep some for stability)
                "GS", "MS",  # Investment banks with less community focus
            ],
            
            'allocation_changes': {
                'increase_esg_allocation': 'Increase ESG-focused investments from 60% to 70%',
                'add_community_impact_allocation': 'Add 10% allocation specifically for community impact investments',
                'reduce_speculative_allocation': 'Reduce speculative investments (crypto, high-vol tech) from 20% to 10%'
            }
        }
        
        return recommendations
    
    def run_full_analysis(self):
        """Run complete alignment analysis"""
        
        print("🤖 AI/NLP ANALYSIS: MTWB Foundation Portfolio Alignment")
        print("=" * 70)
        
        # Case study analysis
        case_analysis = self.analyze_case_study_requirements()
        print(f"\n📋 CASE STUDY ANALYSIS:")
        print(f"   💰 Required Annual Return: {case_analysis['financial_requirements']['required_annual_return']:.1%}")
        print(f"   🎯 Growth Multiple Needed: {case_analysis['financial_requirements']['required_growth_multiple']:.1f}x")
        print(f"   😊 Sentiment Score: {case_analysis['sentiment_score']:.2f} (positive words frequency)")
        
        # Theme analysis
        print(f"\n🎯 KEY THEME FREQUENCY:")
        for theme, score in case_analysis['theme_scores'].items():
            print(f"   {theme.replace('_', ' ').title()}: {score} mentions")
        
        # Portfolio analysis
        portfolio_analysis = self.analyze_current_portfolio_stocks()
        print(f"\n📊 CURRENT PORTFOLIO ANALYSIS:")
        print(f"   📈 Total Stock Universe: {portfolio_analysis['total_stocks']} stocks")
        print(f"   🌱 ESG Categories: {len([c for c, s in portfolio_analysis['esg_scores'].items() if s >= 7])}/6")
        print(f"   🏘️ Community-Focused Categories: {len([c for c, s in portfolio_analysis['community_scores'].items() if s >= 7])}/6")
        
        # Alignment scoring
        alignment = self.calculate_alignment_score()
        print(f"\n🎯 ALIGNMENT ASSESSMENT:")
        print(f"   📊 Overall Alignment Score: {alignment['overall_score']:.1f}/10")
        print(f"   🏆 Grade: {alignment['grade']}")
        
        print(f"\n📈 DETAILED SCORES:")
        for factor, score in alignment['individual_scores'].items():
            weight = alignment['weights'][factor]
            print(f"   {factor.replace('_', ' ').title()}: {score:.1f}/10 (weight: {weight:.0%})")
        
        # Recommendations
        recommendations = self.generate_recommendations()
        print(f"\n🚀 AI RECOMMENDATIONS:")
        print(f"   ➕ Add {len(recommendations['add_stocks'])} ESG/Community-focused stocks")
        print(f"   ➖ Remove {len(recommendations['remove_stocks'])} speculative positions")
        print(f"   🔄 Implement 3 allocation changes for better alignment")
        
        return {
            'case_analysis': case_analysis,
            'portfolio_analysis': portfolio_analysis,
            'alignment_score': alignment,
            'recommendations': recommendations
        }

if __name__ == "__main__":
    analyzer = MTWBAlignmentAnalyzer()
    results = analyzer.run_full_analysis()
    
    print(f"\n🎉 ANALYSIS COMPLETE!")
    print(f"   Current alignment with MTWB goals: {results['alignment_score']['grade']}")
    print(f"   Key improvement area: Increase community-focused investments")
    print(f"   Financial viability: Requires verification of return targets")
