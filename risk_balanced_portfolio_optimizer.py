# connor_barwin_mtwb_portfolio_optimizer.py - Make the World Better Foundation Portfolio
"""
<PERSON>'s Make the World Better Foundation Portfolio Optimizer
Investment Goals:
- Initial Investment: $500,000
- Target: Grow to $1.5M+ over 10 years
- Annual Drawdowns: $10,000 starting year 3 (2029)
- Focus: Community impact, sustainable growth, ESG-aligned investments
- Final Goal: Fund transformative capital project in Philadelphia by 2036
"""

import yfinance as yf
import pandas as pd
import numpy as np
from scipy.optimize import minimize
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class MTWBPortfolioOptimizer:
    """Make the World Better Foundation Portfolio Optimizer"""

    def __init__(self, initial_capital=500000, target_capital=1500000, years_to_target=10, annual_drawdown=10000, drawdown_start_year=3):
        self.initial_capital = initial_capital
        self.target_capital = target_capital
        self.years_to_target = years_to_target
        self.annual_drawdown = annual_drawdown
        self.drawdown_start_year = drawdown_start_year

        # Calculate required annual return considering drawdowns
        self.required_annual_return = self.calculate_required_return()

        print(f"🏈 Connor Barwin's Make the World Better Foundation Portfolio")
        print(f"   💵 Starting Capital: ${initial_capital:,}")
        print(f"   🎯 Target Capital (10 years): ${target_capital:,}")
        print(f"   📉 Annual Drawdown: ${annual_drawdown:,} (starting year {drawdown_start_year})")
        print(f"   📈 Required Annual Return: {self.required_annual_return:.2%}")

    def calculate_required_return(self):
        """Calculate required annual return considering drawdowns"""
        # Simplified calculation - assumes compound growth with periodic withdrawals
        # This is a rough estimate; actual calculation would be more complex
        total_drawdowns = self.annual_drawdown * (self.years_to_target - self.drawdown_start_year + 1)
        adjusted_target = self.target_capital + total_drawdowns
        required_return = (adjusted_target / self.initial_capital) ** (1/self.years_to_target) - 1
        return required_return
    
    def get_growth_focused_tickers(self):
        """Get growth-focused tickers aligned with MTWB's community impact goals"""
        print("\n📈 GROWTH-FOCUSED INVESTMENTS (60% allocation)")
        print("   🎯 Focus: ESG, Community Impact, Sustainable Growth")

        growth_categories = {
            "ESG-Tech-Growth": [
                "MSFT", "GOOGL", "AAPL", "NVDA", "AMD", "CRM", "ADBE", "NOW",
                "TSLA", "NFLX", "META", "AMZN", "SHOP", "SQ", "PYPL", "V"
            ],
            "Clean-Energy-Infrastructure": [
                "ICLN", "PBW", "QCLN", "CNRG", "NEE", "ENPH", "SEDG", "FSLR",
                "BEP", "AES", "XEL", "SO", "DUK", "EXC", "AWK", "WEC"
            ],
            "Healthcare-Innovation": [
                "JNJ", "UNH", "PFE", "ABBV", "LLY", "TMO", "DHR", "ABT",
                "ISRG", "DXCM", "VEEV", "ILMN", "REGN", "GILD", "BIIB", "MRNA"
            ],
            "Education-Community": [
                "CHGG", "LRND", "STRA", "APEI", "CECO", "UTI", "LINC", "PRDO",
                "EDUC", "COUR", "2U", "BOXL", "PCAR", "NABL", "AFYA", "TAL"
            ],
            "Sustainable-Consumer": [
                "COST", "TGT", "WMT", "HD", "LOW", "NKE", "SBUX", "MCD",
                "PG", "UL", "KO", "PEP", "NSRGY", "UN", "CL", "EL"
            ],
            "Financial-Inclusion": [
                "JPM", "BAC", "WFC", "C", "GS", "MS", "BLK", "SCHW",
                "AXP", "V", "MA", "PYPL", "SQ", "AFRM", "UPST", "LC"
            ]
        }
        
        growth_tickers = []
        for category, tickers in growth_categories.items():
            print(f"   📈 {category}: {len(tickers)} tickers")
            growth_tickers.extend(tickers)

        print(f"   🎯 Total Growth-Focused: {len(growth_tickers)} tickers")
        return growth_tickers
    
    def get_stable_income_tickers(self):
        """Get stable income tickers aligned with MTWB's sustainability goals"""
        print("\n📊 STABLE INCOME INVESTMENTS (40% allocation)")
        print("   🎯 Focus: ESG Bonds, Sustainable Infrastructure, Community REITs")

        stable_categories = {
            "ESG-Bonds": [
                "ESGU", "ESGD", "SUSB", "SUSC", "AGG", "BND", "VCIT", "LQD",
                "IGSB", "IGIB", "IGLB", "CORP", "USIG", "SPIB"
            ],
            "Green-Bonds": [
                "GRNB", "FLGR", "GRNL", "ICLN", "PBW", "QCLN", "CNRG",
                "TLT", "IEF", "SHY", "GOVT", "SPTL", "VGIT", "TIPS"
            ],
            "Community-REITs": [
                "VNQ", "XLRE", "RWR", "SCHH", "FREL", "PLD", "EXR", "PSA",
                "AMT", "CCI", "SBAC", "AWK", "WTR", "CWCO", "NEE"
            ],
            "ESG-Dividend": [
                "VYM", "SCHD", "DVY", "VIG", "DGRO", "NOBL", "HDV", "SPHD",
                "SPYD", "VTV", "USMV", "MTUM", "QUAL", "SIZE", "VMOT"
            ],
            "Sustainable-Utilities": [
                "XLU", "VPU", "FUTY", "NEE", "SO", "DUK", "EXC", "AWK",
                "WEC", "AES", "XEL", "BEP", "GRID", "PBW", "URA"
            ],
            "International-ESG": [
                "VEA", "IEFA", "EFA", "VGK", "ESGD", "ESGE", "VSGX",
                "ACWI", "VXUS", "FTIHX", "FXNAX", "VTIAX", "VFWAX"
            ]
        }
        
        stable_tickers = []
        for category, tickers in stable_categories.items():
            print(f"   📊 {category}: {len(tickers)} tickers")
            stable_tickers.extend(tickers)

        print(f"   🎯 Total Stable Income: {len(stable_tickers)} tickers")
        return stable_tickers
    
    def fetch_data(self, tickers, max_tickers=50):
        """Fetch price data for tickers"""
        # Limit tickers for manageable processing
        if len(tickers) > max_tickers:
            tickers = tickers[:max_tickers]
        
        print(f"   📥 Fetching data for {len(tickers)} tickers...")
        
        # Date range: 1 year back
        end_date = datetime.now().date()
        start_date = (datetime.now() - timedelta(days=365)).date()
        
        try:
            data = yf.download(tickers, start=start_date, end=end_date, progress=False)
            
            if data.empty:
                return pd.DataFrame(), tickers
            
            # Handle multi-level columns
            if 'Close' in data.columns:
                close_data = data['Close']
            elif isinstance(data.columns, pd.MultiIndex):
                close_data = data.xs('Close', level=1, axis=1)
            else:
                close_data = data
            
            # Remove tickers with insufficient data
            valid_tickers = []
            failed = []
            
            for ticker in tickers:
                if ticker in close_data.columns:
                    ticker_data = close_data[ticker].dropna()
                    if len(ticker_data) > 100:  # Need sufficient data
                        valid_tickers.append(ticker)
                    else:
                        failed.append(ticker)
                else:
                    failed.append(ticker)
            
            if valid_tickers:
                final_data = close_data[valid_tickers]
                print(f"   ✅ Successfully fetched {len(valid_tickers)} tickers")
                if failed:
                    print(f"   ❌ Failed: {len(failed)} tickers")
                return final_data, failed
            else:
                return pd.DataFrame(), tickers
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return pd.DataFrame(), tickers
    
    def optimize_risk_bucket(self, data, risk_type, max_weight=0.15):
        """Optimize within a risk bucket"""
        print(f"\n🎯 Optimizing {risk_type} Portfolio")
        
        if data.empty or len(data.columns) < 3:
            print(f"   ❌ Insufficient data for {risk_type}")
            return None, None
        
        # Calculate returns
        returns = data.pct_change().dropna()
        mean_returns = returns.mean()
        cov_matrix = returns.cov()
        
        # Add regularization
        cov_matrix += np.eye(len(cov_matrix)) * 1e-6
        
        print(f"   📊 Assets: {len(mean_returns)}")
        print(f"   📈 Avg Daily Return: {mean_returns.mean():.6f}")
        print(f"   📊 Avg Daily Volatility: {returns.std().mean():.6f}")
        
        # Optimization function
        def neg_sharpe(weights):
            port_return = np.dot(weights, mean_returns) * 252
            port_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix * 252, weights)))
            if port_vol <= 0:
                return 999
            return -(port_return - 0.03) / port_vol
        
        # Constraints and bounds
        n_assets = len(mean_returns)
        constraints = [{'type': 'eq', 'fun': lambda x: np.sum(x) - 1}]
        bounds = tuple((0, max_weight) for _ in range(n_assets))
        
        # Initial weights
        initial_weights = np.ones(n_assets) / n_assets
        
        try:
            result = minimize(
                neg_sharpe,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': 1000}
            )
            
            if result.success:
                weights = result.x
                port_return = np.dot(weights, mean_returns) * 252
                port_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix * 252, weights)))
                sharpe = (port_return - 0.03) / port_vol
                
                print(f"   ✅ Optimization successful!")
                print(f"   📈 Expected Return: {port_return:.2%}")
                print(f"   📊 Volatility: {port_vol:.2%}")
                print(f"   ⚡ Sharpe Ratio: {sharpe:.3f}")
                
                return weights, {
                    'return': port_return,
                    'volatility': port_vol,
                    'sharpe': sharpe,
                    'tickers': data.columns.tolist()
                }
            else:
                print(f"   ❌ Optimization failed")
                return None, None
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return None, None
    
    def simulate_portfolio_performance(self, combined_weights, combined_data, weeks=10):
        """Simulate combined portfolio performance"""
        print(f"\n🔮 SIMULATING PORTFOLIO PERFORMANCE")
        print("=" * 60)
        
        returns = combined_data.pct_change().dropna()
        mean_returns = returns.mean()
        cov_matrix = returns.cov()
        
        # Portfolio metrics
        port_daily_return = np.dot(combined_weights, mean_returns.values)
        port_daily_vol = np.sqrt(np.dot(combined_weights.T, np.dot(cov_matrix.values, combined_weights)))
        
        # Simulation
        days = weeks * 5
        simulations = []
        
        print(f"   🎯 Running 1,000 simulations for {weeks} weeks...")
        
        for _ in range(1000):
            random_returns = np.random.normal(port_daily_return, port_daily_vol, days)
            portfolio_values = [self.initial_capital]
            for daily_return in random_returns:
                portfolio_values.append(portfolio_values[-1] * (1 + daily_return))
            simulations.append(portfolio_values[1:])
        
        simulations = np.array(simulations)
        
        # Statistics
        mean_simulation = np.mean(simulations, axis=0)
        percentile_5 = np.percentile(simulations, 5, axis=0)
        percentile_95 = np.percentile(simulations, 95, axis=0)
        
        # Visualization
        future_dates = pd.date_range(start=datetime.now(), periods=days, freq='D')
        
        plt.figure(figsize=(15, 10))
        
        # Main simulation plot
        plt.subplot(2, 2, (1, 2))
        plt.fill_between(future_dates, percentile_5, percentile_95, alpha=0.2, color='lightblue', label='90% Confidence')
        plt.plot(future_dates, mean_simulation, linewidth=3, color='darkblue', label='Expected Value')
        plt.axhline(y=self.initial_capital, color='red', linestyle='--', alpha=0.7, label=f'Starting: ${self.initial_capital:,}')
        
        plt.title(f'💰 Risk-Balanced Portfolio Simulation - ${self.initial_capital:,} Starting Capital', fontsize=16, fontweight='bold')
        plt.xlabel('Date')
        plt.ylabel('Portfolio Value ($)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        
        # Final value annotation
        final_expected = mean_simulation[-1]
        expected_return = (final_expected - self.initial_capital) / self.initial_capital * 100
        
        plt.annotate(f'Expected: ${final_expected:,.0f}\nReturn: {expected_return:+.1f}%',
                    xy=(future_dates[-1], final_expected),
                    xytext=(-120, 20), textcoords='offset points',
                    bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.8),
                    fontsize=11, fontweight='bold')
        
        # MTWB allocation pie chart
        plt.subplot(2, 2, 3)
        allocations = [0.60, 0.40]  # 60% growth, 40% stable
        labels = ['Growth-Focused (60%)', 'Stable Income (40%)']
        colors = ['darkblue', 'darkgreen']
        plt.pie(allocations, labels=labels, colors=colors, autopct='%1.0f%%', startangle=90)
        plt.title('🎯 MTWB ESG Allocation', fontsize=14, fontweight='bold')
        
        # Metrics display
        plt.subplot(2, 2, 4)
        plt.axis('off')
        
        annual_return = port_daily_return * 252
        annual_vol = port_daily_vol * np.sqrt(252)
        sharpe = (annual_return - 0.03) / annual_vol
        
        metrics_text = f"""
🏈 MTWB PORTFOLIO METRICS

💵 Starting Capital: ${self.initial_capital:,}
🎯 Target Capital (10yr): ${self.target_capital:,}
📈 Required Return: {self.required_annual_return:.2%}
📈 Expected Annual Return: {annual_return:.2%}
📊 Annual Volatility: {annual_vol:.2%}
⚡ Sharpe Ratio: {sharpe:.3f}

🔮 {weeks}-WEEK PROJECTION:
💰 Expected Value: ${final_expected:,.0f}
📈 Expected Return: {expected_return:+.2f}%
📊 5th Percentile: ${percentile_5[-1]:,.0f}
📊 95th Percentile: ${percentile_95[-1]:,.0f}
📊 Profit Probability: {(simulations[:, -1] > self.initial_capital).mean()*100:.1f}%

🎯 ESG ALLOCATION:
📈 Growth-Focused: ${self.initial_capital * 0.60:,.0f}
📊 Stable Income: ${self.initial_capital * 0.40:,.0f}
"""
        
        plt.text(0.05, 0.95, metrics_text, transform=plt.gca().transAxes,
                fontsize=10, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout()
        plt.show()
        
        # Print summary
        print(f"\n📊 SIMULATION RESULTS:")
        print(f"   💰 Expected Final Value: ${final_expected:,.0f}")
        print(f"   📈 Expected Return: {expected_return:+.2f}%")
        print(f"   📊 5th Percentile: ${percentile_5[-1]:,.0f}")
        print(f"   📊 95th Percentile: ${percentile_95[-1]:,.0f}")
        print(f"   📊 Probability of Profit: {(simulations[:, -1] > self.initial_capital).mean()*100:.1f}%")
        
        return {
            'expected_value': final_expected,
            'expected_return': expected_return,
            'annual_return': annual_return,
            'annual_volatility': annual_vol,
            'sharpe_ratio': sharpe,
            'profit_probability': (simulations[:, -1] > self.initial_capital).mean()
        }

def main():
    """Main execution for Connor Barwin's MTWB Foundation Portfolio"""
    print("🏈 CONNOR BARWIN'S MAKE THE WORLD BETTER FOUNDATION PORTFOLIO")
    print("=" * 70)

    # Initialize optimizer with MTWB-specific parameters
    optimizer = MTWBPortfolioOptimizer(
        initial_capital=500000,
        target_capital=1500000,
        years_to_target=10,
        annual_drawdown=10000,
        drawdown_start_year=3
    )
    
    try:
        # Get tickers aligned with MTWB goals
        growth_tickers = optimizer.get_growth_focused_tickers()
        stable_tickers = optimizer.get_stable_income_tickers()

        # Fetch data
        print(f"\n📊 FETCHING MARKET DATA")
        print("=" * 50)

        growth_data, _ = optimizer.fetch_data(growth_tickers, max_tickers=50)
        stable_data, _ = optimizer.fetch_data(stable_tickers, max_tickers=40)

        if growth_data.empty or stable_data.empty:
            print("❌ Insufficient data for optimization")
            return None

        # Optimize each bucket with MTWB-appropriate allocations
        growth_weights, growth_metrics = optimizer.optimize_risk_bucket(growth_data, "GROWTH-FOCUSED", max_weight=0.15)
        stable_weights, stable_metrics = optimizer.optimize_risk_bucket(stable_data, "STABLE INCOME", max_weight=0.20)
        
        if growth_weights is None or stable_weights is None:
            print("❌ Optimization failed")
            return None

        # Combine portfolios with MTWB allocation (60% growth, 40% stable)
        print(f"\n🎯 COMBINING MTWB ESG-ALIGNED PORTFOLIO")
        print("=" * 50)

        # Create combined data and weights
        combined_data = pd.concat([growth_data, stable_data], axis=1)
        combined_weights = np.zeros(len(combined_data.columns))

        # Apply MTWB allocations (60% growth, 40% stable)
        growth_allocation = 0.60
        stable_allocation = 0.40

        growth_start_idx = 0
        growth_end_idx = len(growth_data.columns)
        stable_start_idx = growth_end_idx
        stable_end_idx = len(combined_data.columns)

        combined_weights[growth_start_idx:growth_end_idx] = growth_weights * growth_allocation
        combined_weights[stable_start_idx:stable_end_idx] = stable_weights * stable_allocation

        # Portfolio metrics
        combined_return = (growth_metrics['return'] * growth_allocation +
                          stable_metrics['return'] * stable_allocation)
        combined_vol = np.sqrt((growth_metrics['volatility'] * growth_allocation)**2 +
                              (stable_metrics['volatility'] * stable_allocation)**2)
        combined_sharpe = (combined_return - 0.03) / combined_vol
        
        print(f"   📈 Combined Expected Return: {combined_return:.2%}")
        print(f"   📊 Combined Volatility: {combined_vol:.2%}")
        print(f"   ⚡ Combined Sharpe Ratio: {combined_sharpe:.3f}")
        
        # Show top holdings
        print(f"\n🏆 TOP HOLDINGS BY INVESTMENT CATEGORY:")
        print(f"\n📈 GROWTH-FOCUSED (60% = ${optimizer.initial_capital * 0.60:,.0f}):")
        growth_top = sorted(zip(growth_data.columns, growth_weights), key=lambda x: x[1], reverse=True)[:8]
        for i, (ticker, weight) in enumerate(growth_top, 1):
            allocation = weight * growth_allocation
            dollar_amount = allocation * optimizer.initial_capital
            print(f"   {i:2d}. {ticker:8s} | {allocation:6.2%} | ${dollar_amount:,.0f}")

        print(f"\n📊 STABLE INCOME (40% = ${optimizer.initial_capital * 0.40:,.0f}):")
        stable_top = sorted(zip(stable_data.columns, stable_weights), key=lambda x: x[1], reverse=True)[:8]
        for i, (ticker, weight) in enumerate(stable_top, 1):
            allocation = weight * stable_allocation
            dollar_amount = allocation * optimizer.initial_capital
            print(f"   {i:2d}. {ticker:8s} | {allocation:6.2%} | ${dollar_amount:,.0f}")
        
        # Run simulation
        simulation_results = optimizer.simulate_portfolio_performance(combined_weights, combined_data, weeks=10)
        
        print(f"\n🎉 MTWB ESG-ALIGNED PORTFOLIO OPTIMIZATION COMPLETE!")
        print(f"   💰 Starting Capital: ${optimizer.initial_capital:,}")
        print(f"   🎯 Target Capital (10 years): ${optimizer.target_capital:,}")
        print(f"   📈 Required Annual Return: {optimizer.required_annual_return:.2%}")
        print(f"   📈 Expected Annual Return: {combined_return:.2%}")
        print(f"   📊 Annual Volatility: {combined_vol:.2%}")
        print(f"   ⚡ Sharpe Ratio: {combined_sharpe:.3f}")
        print(f"   🔮 10-Week Expected Return: {simulation_results['expected_return']:+.2f}%")

        # Check if portfolio meets MTWB requirements
        meets_target = combined_return >= optimizer.required_annual_return
        print(f"   {'✅' if meets_target else '❌'} Meets MTWB Target: {meets_target}")
        if not meets_target:
            shortfall = optimizer.required_annual_return - combined_return
            print(f"   📉 Return Shortfall: {shortfall:.2%} annually")
        
        return combined_data, combined_weights, simulation_results
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = main()
