# global_portfolio_optimizer.py - Global Portfolio Optimization with All World Stocks + Bonds
"""
Global Portfolio Optimizer using yahoo_fin to get all tickers from major indices worldwide
Includes US, International, and Bond markets for comprehensive global optimization
"""

import yfinance as yf
import pandas as pd
import numpy as np
from scipy.optimize import minimize
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
import time
warnings.filterwarnings('ignore')

# Try to import yahoo_fin, install if not available
try:
    from yahoo_fin import stock_info as si
    print("✅ yahoo_fin imported successfully")
except ImportError:
    print("❌ yahoo_fin not found. Installing...")
    import subprocess
    subprocess.check_call(["pip", "install", "yahoo_fin"])
    from yahoo_fin import stock_info as si
    print("✅ yahoo_fin installed and imported")

class GlobalPortfolioOptimizer:
    """Comprehensive global portfolio optimizer"""
    
    def __init__(self, max_tickers_per_index=50, include_bonds=True):
        self.max_tickers_per_index = max_tickers_per_index
        self.include_bonds = include_bonds
        self.all_tickers = []
        self.ticker_sources = {}
        
        print(f"🌍 Global Portfolio Optimizer initialized")
        print(f"   Max tickers per index: {max_tickers_per_index}")
        print(f"   Include bonds: {include_bonds}")
    
    def get_us_index_tickers(self):
        """Get tickers from major US indices with fallback methods"""
        print("\n🇺🇸 Fetching US Index Tickers...")
        us_tickers = []

        # Try yahoo_fin first
        try:
            # S&P 500
            print("   📊 S&P 500...", end="")
            sp500 = si.tickers_sp500()
            selected_sp500 = sp500[:self.max_tickers_per_index]
            us_tickers.extend(selected_sp500)
            for ticker in selected_sp500:
                self.ticker_sources[ticker] = "S&P 500"
            print(f" ✅ {len(selected_sp500)} tickers")

        except Exception as e:
            print(f" ❌ Error: {e}")
            # Fallback: Use predefined S&P 500 tickers
            print("   📊 S&P 500 (fallback)...", end="")
            sp500_fallback = [
                "AAPL", "MSFT", "GOOGL", "AMZN", "NVDA", "META", "TSLA", "BRK-B", "AVGO", "JPM",
                "LLY", "UNH", "XOM", "V", "PG", "JNJ", "MA", "HD", "CVX", "MRK",
                "ABBV", "COST", "PEP", "KO", "WMT", "BAC", "CRM", "TMO", "NFLX", "ACN",
                "LIN", "MCD", "ABT", "CSCO", "AMD", "DHR", "VZ", "ADBE", "WFC", "PM",
                "NOW", "NEE", "RTX", "SPGI", "CAT", "IBM", "GS", "QCOM", "HON", "INTU"
            ][:self.max_tickers_per_index]
            us_tickers.extend(sp500_fallback)
            for ticker in sp500_fallback:
                self.ticker_sources[ticker] = "S&P 500"
            print(f" ✅ {len(sp500_fallback)} tickers")

        try:
            # NASDAQ
            print("   📊 NASDAQ...", end="")
            nasdaq = si.tickers_nasdaq()
            nasdaq_clean = [t for t in nasdaq if len(t) <= 5 and '.' not in t and '^' not in t][:self.max_tickers_per_index]
            us_tickers.extend(nasdaq_clean)
            for ticker in nasdaq_clean:
                self.ticker_sources[ticker] = "NASDAQ"
            print(f" ✅ {len(nasdaq_clean)} tickers")

        except Exception as e:
            print(f" ❌ Error: {e}")
            # Fallback: Use predefined NASDAQ tickers
            print("   📊 NASDAQ (fallback)...", end="")
            nasdaq_fallback = [
                "AAPL", "MSFT", "GOOGL", "AMZN", "NVDA", "META", "TSLA", "AVGO", "NFLX", "ADBE",
                "COST", "PEP", "CSCO", "AMD", "QCOM", "INTU", "CMCSA", "HON", "TXN", "AMGN",
                "SBUX", "GILD", "MDLZ", "ADP", "ISRG", "BKNG", "ADI", "VRTX", "REGN", "LRCX"
            ][:self.max_tickers_per_index]
            us_tickers.extend(nasdaq_fallback)
            for ticker in nasdaq_fallback:
                self.ticker_sources[ticker] = "NASDAQ"
            print(f" ✅ {len(nasdaq_fallback)} tickers")

        try:
            # Dow Jones
            print("   📊 Dow Jones...", end="")
            dow = si.tickers_dow()
            us_tickers.extend(dow)
            for ticker in dow:
                self.ticker_sources[ticker] = "Dow Jones"
            print(f" ✅ {len(dow)} tickers")

        except Exception as e:
            print(f" ❌ Error: {e}")
            # Fallback: Use predefined Dow tickers
            print("   📊 Dow Jones (fallback)...", end="")
            dow_fallback = [
                "AAPL", "MSFT", "UNH", "GS", "HD", "CAT", "MCD", "AMGN", "V", "AXP",
                "BA", "TRV", "JPM", "IBM", "JNJ", "PG", "CVX", "MRK", "KO", "DIS",
                "MMM", "DOW", "NKE", "CRM", "HON", "INTC", "CSCO", "VZ", "WMT", "WBA"
            ]
            us_tickers.extend(dow_fallback)
            for ticker in dow_fallback:
                self.ticker_sources[ticker] = "Dow Jones"
            print(f" ✅ {len(dow_fallback)} tickers")

        # Add Russell 2000 and other major US tickers
        print("   📊 Additional US Large Caps...", end="")
        additional_us = [
            "BRK-A", "GOOGL", "GOOG", "UNH", "JNJ", "V", "PG", "JPM", "MA", "HD",
            "NVDA", "DIS", "PYPL", "ADBE", "NFLX", "CRM", "TMO", "ACN", "COST", "AVGO",
            "ABT", "CVX", "LLY", "NKE", "MRK", "PFE", "KO", "PEP", "WMT", "INTC",
            "CSCO", "VZ", "XOM", "BA", "IBM", "GS", "CAT", "TRV", "AXP", "MMM"
        ][:self.max_tickers_per_index]
        us_tickers.extend(additional_us)
        for ticker in additional_us:
            self.ticker_sources[ticker] = "US Large Cap"
        print(f" ✅ {len(additional_us)} tickers")

        # Remove duplicates while preserving order
        us_tickers_unique = list(dict.fromkeys(us_tickers))
        print(f"   🎯 Total unique US tickers: {len(us_tickers_unique)}")

        return us_tickers_unique
    
    def get_international_tickers(self):
        """Get international tickers from major global indices"""
        print("\n🌍 Fetching International Tickers...")
        intl_tickers = []

        # Major international indices and their representative ETFs/stocks
        international_markets = {
            "Europe-Large": [
                "ASML", "NESN.SW", "LVMH.PA", "SAP", "TM", "UL", "NVO", "RYDAF",
                "SHEL.L", "AZN.L", "ULVR.L", "VOD.L", "BP.L", "HSBA.L", "GSK.L",
                "SAP.DE", "SIE.DE", "ALV.DE", "BAS.DE", "BMW.DE", "DAI.DE",
                "MC.PA", "OR.PA", "SAN.PA", "BNP.PA", "TTE.PA", "AIR.PA",
                "NOVN.SW", "ROG.SW", "UHR.SW", "LONN.SW", "ABB.SW"
            ],
            "Asia-Pacific-Major": [
                "TSM", "BABA", "TCEHY", "SONY", "7203.T", "005930.KS", "2330.TW",
                "6758.T", "6861.T", "9984.T", "7974.T", "6098.T", "4063.T",
                "000858.SZ", "000002.SZ", "600036.SS", "600519.SS", "000001.SZ"
            ],
            "Emerging-Markets": [
                "VALE", "ITUB", "PBR", "BIDU", "JD", "NTES", "WIT", "ABEV",
                "PETR4.SA", "VALE3.SA", "ITUB4.SA", "BBDC4.SA", "B3SA3.SA",
                "WEGE3.SA", "MGLU3.SA", "RENT3.SA", "LREN3.SA"
            ],
            "Canada-TSX": [
                "SHOP.TO", "CNR.TO", "RY.TO", "TD.TO", "BNS.TO", "BMO.TO",
                "ENB.TO", "TRP.TO", "SU.TO", "CNQ.TO", "CP.TO", "WCN.TO",
                "BAM.TO", "MFC.TO", "SLF.TO", "FFH.TO", "ATD.TO"
            ],
            "Australia-ASX": [
                "BHP.AX", "CBA.AX", "CSL.AX", "WBC.AX", "ANZ.AX", "NAB.AX",
                "WES.AX", "WOW.AX", "TLS.AX", "RIO.AX", "FMG.AX", "TCL.AX",
                "MQG.AX", "WDS.AX", "COL.AX", "JHX.AX"
            ],
            "UK-FTSE": [
                "SHEL.L", "AZN.L", "ULVR.L", "VOD.L", "BP.L", "HSBA.L", "GSK.L",
                "LLOY.L", "BT-A.L", "BARC.L", "RIO.L", "GLEN.L", "PRU.L",
                "DGE.L", "REL.L", "AAL.L", "LSEG.L", "NWG.L"
            ],
            "Germany-DAX": [
                "SAP.DE", "SIE.DE", "ALV.DE", "BAS.DE", "BMW.DE", "DAI.DE",
                "VOW3.DE", "MUV2.DE", "DTE.DE", "DB1.DE", "CON.DE", "HEN3.DE",
                "FRE.DE", "IFX.DE", "MRK.DE", "RWE.DE"
            ],
            "France-CAC": [
                "MC.PA", "OR.PA", "SAN.PA", "BNP.PA", "TTE.PA", "AIR.PA",
                "CAP.PA", "EL.PA", "RMS.PA", "KER.PA", "VIV.PA", "DG.PA",
                "CS.PA", "ACA.PA", "BN.PA", "ML.PA"
            ],
            "Japan-Nikkei": [
                "6758.T", "6861.T", "9984.T", "7974.T", "6098.T", "4063.T",
                "7203.T", "8306.T", "9432.T", "4519.T", "6367.T", "6954.T",
                "8035.T", "4502.T", "6501.T", "7751.T"
            ],
            "India-NSE": [
                "RELIANCE.NS", "TCS.NS", "INFY.NS", "HDFCBANK.NS", "ICICIBANK.NS",
                "HINDUNILVR.NS", "ITC.NS", "SBIN.NS", "BHARTIARTL.NS", "KOTAKBANK.NS",
                "LT.NS", "HCLTECH.NS", "ASIANPAINT.NS", "MARUTI.NS", "TITAN.NS"
            ],
            "China-HK": [
                "0700.HK", "0941.HK", "1299.HK", "0939.HK", "2318.HK", "1398.HK",
                "3690.HK", "0388.HK", "2628.HK", "1810.HK", "0883.HK", "1024.HK",
                "2382.HK", "1211.HK", "0175.HK", "0016.HK"
            ],
            "South-Korea": [
                "005930.KS", "000660.KS", "035420.KS", "005380.KS", "051910.KS",
                "006400.KS", "035720.KS", "028260.KS", "055550.KS", "105560.KS"
            ],
            "Taiwan": [
                "2330.TW", "2454.TW", "2317.TW", "1303.TW", "2412.TW",
                "1301.TW", "2881.TW", "2002.TW", "3711.TW", "2886.TW"
            ]
        }

        for region, tickers in international_markets.items():
            print(f"   🌏 {region}...", end="")
            intl_tickers.extend(tickers)
            for ticker in tickers:
                self.ticker_sources[ticker] = f"International-{region}"
            print(f" ✅ {len(tickers)} tickers")

        print(f"   🎯 Total international tickers: {len(intl_tickers)}")
        return intl_tickers
    
    def get_bond_tickers(self):
        """Get comprehensive bond ETFs and treasury securities"""
        print("\n💰 Fetching Bond Tickers...")

        bond_categories = {
            "US-Treasury": [
                "TLT", "IEF", "SHY", "GOVT", "SPTL", "VGIT", "VGLT", "VGSH",
                "BIL", "SHV", "SGOV", "TBIL", "FXNAX", "VFISX"
            ],
            "US-Corporate": [
                "LQD", "VCIT", "VCLT", "VCSH", "AGG", "BND", "IGSB", "IGIB", "IGLB",
                "CORP", "USIG", "SPIB", "IEFA", "QLTA", "QUAL", "FIXD"
            ],
            "International-Bonds": [
                "BNDX", "IAGG", "IGOV", "BWX", "VWOB", "EMB", "PCY", "VGIT",
                "VTEB", "PICB", "FLOT", "JPST", "NEAR", "MINT", "GSY"
            ],
            "High-Yield": [
                "HYG", "JNK", "USHY", "SHYG", "HYEM", "EMHY", "ANGL", "FALN",
                "HYLB", "SJNK", "BKLN", "SRLN", "FLRN", "HYLS"
            ],
            "Inflation-Protected": [
                "TIPS", "VTIP", "SCHP", "STIP", "TDTT", "GTIP", "SPIP", "LTPZ",
                "IPE", "PBTP", "RINF", "CPI", "IVOL", "DFIP"
            ],
            "Municipal-Bonds": [
                "MUB", "VTEB", "TFI", "SMB", "HYD", "MUA", "MUC", "MUI",
                "MMIT", "MMIN", "PZA", "PMF", "MYI", "MYC", "MYD", "MYF"
            ],
            "Floating-Rate": [
                "FLOT", "FLRN", "FLTR", "TFLO", "USFR", "JPST", "MINT", "GSY",
                "ICSH", "NEAR", "BIL", "SHV", "SGOV", "TBIL"
            ],
            "Convertible-Bonds": [
                "CWB", "ICVT", "TAWK", "CVRT", "CNVS", "BOND"
            ]
        }

        bond_tickers = []
        for category, tickers in bond_categories.items():
            print(f"   💰 {category}...", end="")
            bond_tickers.extend(tickers)
            for ticker in tickers:
                self.ticker_sources[ticker] = f"Bonds-{category}"
            print(f" ✅ {len(tickers)} tickers")

        # Remove duplicates
        bond_tickers = list(dict.fromkeys(bond_tickers))

        print(f"   💰 Total unique bond tickers: {len(bond_tickers)}")
        return bond_tickers
    
    def get_commodity_and_alternative_tickers(self):
        """Get commodity and alternative investment tickers"""
        print("\n🏗️ Fetching Commodity & Alternative Tickers...")
        
        commodity_tickers = [
            # Precious Metals
            "GLD", "SLV", "PPLT", "PALL", "IAU", "SGOL",
            # Energy
            "USO", "UNG", "DBO", "BNO",
            # Agriculture
            "DBA", "CORN", "WEAT", "SOYB", "CANE",
            # Industrial Metals
            "COPX", "SIL", "REMX",
            # Real Estate
            "VNQ", "XLRE", "RWR", "SCHH", "FREL",
            # Crypto (Bitcoin/Ethereum ETFs)
            "BITO", "ETHE", "GBTC"
        ]
        
        for ticker in commodity_tickers:
            self.ticker_sources[ticker] = "Commodities/Alternatives"
        
        print(f"   🏗️ Commodity/Alternative tickers: {len(commodity_tickers)}")
        return commodity_tickers
    
    def collect_all_tickers(self):
        """Collect all tickers from various sources"""
        print("\n🎯 COLLECTING ALL GLOBAL TICKERS")
        print("=" * 60)
        
        all_tickers = []
        
        # US Markets
        us_tickers = self.get_us_index_tickers()
        all_tickers.extend(us_tickers)
        
        # International Markets
        intl_tickers = self.get_international_tickers()
        all_tickers.extend(intl_tickers)
        
        # Bonds
        if self.include_bonds:
            bond_tickers = self.get_bond_tickers()
            all_tickers.extend(bond_tickers)
        
        # Commodities and Alternatives
        commodity_tickers = self.get_commodity_and_alternative_tickers()
        all_tickers.extend(commodity_tickers)
        
        # Remove duplicates
        self.all_tickers = list(dict.fromkeys(all_tickers))
        
        print(f"\n🌍 GLOBAL TICKER COLLECTION COMPLETE:")
        print(f"   📊 Total unique tickers: {len(self.all_tickers)}")
        
        # Show breakdown by source
        source_counts = {}
        for ticker, source in self.ticker_sources.items():
            source_counts[source] = source_counts.get(source, 0) + 1
        
        print(f"\n📊 BREAKDOWN BY SOURCE:")
        for source, count in sorted(source_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"   {source}: {count} tickers")
        
        return self.all_tickers
    
    def fetch_global_data(self, sample_size=200):
        """Fetch data for a sample of global tickers"""
        print(f"\n📊 FETCHING GLOBAL DATA")
        print("=" * 60)
        
        if not self.all_tickers:
            self.collect_all_tickers()
        
        # Sample tickers for manageable processing
        if len(self.all_tickers) > sample_size:
            print(f"   🎯 Sampling {sample_size} tickers from {len(self.all_tickers)} total")
            
            # Stratified sampling to ensure representation from each source
            sampled_tickers = []
            source_groups = {}
            
            for ticker in self.all_tickers:
                source = self.ticker_sources.get(ticker, "Unknown")
                if source not in source_groups:
                    source_groups[source] = []
                source_groups[source].append(ticker)
            
            # Sample proportionally from each source
            for source, tickers in source_groups.items():
                sample_count = min(len(tickers), max(1, int(sample_size * len(tickers) / len(self.all_tickers))))
                sampled_tickers.extend(tickers[:sample_count])
            
            tickers_to_fetch = sampled_tickers[:sample_size]
        else:
            tickers_to_fetch = self.all_tickers
        
        print(f"   📥 Fetching data for {len(tickers_to_fetch)} tickers...")
        
        # Set date range: 1 year back to today
        end_date = datetime.now().date()
        start_date = (datetime.now() - timedelta(days=365)).date()
        
        print(f"   📅 Date range: {start_date} to {end_date}")
        
        # Fetch data in batches to avoid timeouts
        batch_size = 50
        all_data = {}
        failed_tickers = []
        
        for i in range(0, len(tickers_to_fetch), batch_size):
            batch = tickers_to_fetch[i:i+batch_size]
            print(f"   🔄 Batch {i//batch_size + 1}/{(len(tickers_to_fetch)-1)//batch_size + 1}: {len(batch)} tickers")
            
            try:
                batch_data = yf.download(batch, start=start_date, end=end_date, progress=False)
                
                if not batch_data.empty:
                    if 'Close' in batch_data.columns:
                        close_data = batch_data['Close']
                    elif isinstance(batch_data.columns, pd.MultiIndex):
                        close_data = batch_data.xs('Close', level=1, axis=1)
                    else:
                        close_data = batch_data
                    
                    # Add valid tickers to all_data
                    for ticker in batch:
                        if ticker in close_data.columns:
                            ticker_data = close_data[ticker].dropna()
                            if len(ticker_data) > 50:  # Minimum data requirement
                                all_data[ticker] = close_data[ticker]
                            else:
                                failed_tickers.append(ticker)
                        else:
                            failed_tickers.append(ticker)
                
                # Small delay between batches
                time.sleep(1)
                
            except Exception as e:
                print(f"     ❌ Batch error: {str(e)[:100]}")
                failed_tickers.extend(batch)
        
        if all_data:
            final_data = pd.DataFrame(all_data)
            print(f"   ✅ Successfully fetched {len(final_data.columns)} tickers")
            print(f"   ❌ Failed: {len(failed_tickers)} tickers")
            return final_data, failed_tickers
        else:
            print(f"   ❌ No data fetched successfully")
            return pd.DataFrame(), tickers_to_fetch

    def optimize_global_portfolio(self, data, risk_free_rate=0.03, max_weight=0.05):
        """Optimize the global portfolio using robust mean-variance optimization"""
        print(f"\n🎯 OPTIMIZING GLOBAL PORTFOLIO")
        print("=" * 60)

        # Calculate returns and clean data
        returns = data.pct_change().dropna()

        # Remove assets with extreme volatility or insufficient data
        valid_assets = []
        for col in returns.columns:
            asset_returns = returns[col].dropna()
            if len(asset_returns) > 50 and asset_returns.std() < 0.2:  # Less than 20% daily volatility
                valid_assets.append(col)

        if len(valid_assets) < 10:
            print(f"   ❌ Insufficient valid assets: {len(valid_assets)}")
            return None, {'success': False, 'error': 'Insufficient valid assets'}

        # Use only valid assets
        returns = returns[valid_assets]
        mean_returns = returns.mean()
        cov_matrix = returns.cov()

        # Add regularization to covariance matrix
        cov_matrix += np.eye(len(cov_matrix)) * 1e-6

        print(f"   📊 Valid assets: {len(mean_returns)}")
        print(f"   📈 Average daily return: {mean_returns.mean():.6f}")
        print(f"   📊 Average daily volatility: {returns.std().mean():.6f}")

        # Try multiple optimization approaches
        optimization_methods = ['SLSQP', 'trust-constr', 'L-BFGS-B']

        for method in optimization_methods:
            print(f"   🎯 Trying {method} optimization with max {max_weight:.1%} per asset...")

            try:
                # Optimization function (maximize Sharpe ratio)
                def neg_sharpe(weights):
                    weights = np.array(weights)
                    port_return = np.dot(weights, mean_returns) * 252
                    port_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix * 252, weights)))
                    if port_vol <= 0:
                        return 999
                    return -(port_return - risk_free_rate) / port_vol

                # Constraints and bounds
                n_assets = len(mean_returns)
                constraints = [{'type': 'eq', 'fun': lambda x: np.sum(x) - 1}]
                bounds = tuple((0, max_weight) for _ in range(n_assets))

                # Better initial guess - start with some concentration in low-vol assets
                vol_ranking = returns.std().rank()
                initial_weights = np.ones(n_assets) / n_assets
                # Give slightly more weight to lower volatility assets
                for i, vol_rank in enumerate(vol_ranking):
                    if vol_rank <= n_assets * 0.3:  # Bottom 30% volatility
                        initial_weights[i] *= 1.5
                initial_weights = initial_weights / initial_weights.sum()  # Normalize

                result = minimize(
                    neg_sharpe,
                    initial_weights,
                    method=method,
                    bounds=bounds,
                    constraints=constraints,
                    options={'maxiter': 2000, 'ftol': 1e-9}
                )

                if result.success and np.all(result.x >= 0) and abs(np.sum(result.x) - 1) < 1e-6:
                    optimal_weights = result.x

                    # Calculate portfolio metrics
                    port_return = np.dot(optimal_weights, mean_returns) * 252
                    port_vol = np.sqrt(np.dot(optimal_weights.T, np.dot(cov_matrix * 252, optimal_weights)))
                    sharpe_ratio = (port_return - risk_free_rate) / port_vol

                    print(f"   ✅ Optimization successful with {method}!")
                    print(f"   📈 Expected Annual Return: {port_return:.2%}")
                    print(f"   📊 Annual Volatility: {port_vol:.2%}")
                    print(f"   ⚡ Sharpe Ratio: {sharpe_ratio:.3f}")

                    # Create full weights array (including filtered assets)
                    full_weights = np.zeros(len(data.columns))
                    for i, asset in enumerate(valid_assets):
                        asset_idx = data.columns.get_loc(asset)
                        full_weights[asset_idx] = optimal_weights[i]

                    return full_weights, {
                        'return': port_return,
                        'volatility': port_vol,
                        'sharpe': sharpe_ratio,
                        'success': True,
                        'method': method,
                        'valid_assets': len(valid_assets)
                    }
                else:
                    print(f"   ❌ {method} failed: {result.message if hasattr(result, 'message') else 'Invalid solution'}")

            except Exception as e:
                print(f"   ❌ {method} error: {e}")
                continue

        # If all methods fail, use equal weights for valid low-vol assets
        print(f"   🔄 Fallback: Using equal weights for low-volatility assets...")
        low_vol_assets = returns.std().nsmallest(min(20, len(returns.columns))).index

        full_weights = np.zeros(len(data.columns))
        weight_per_asset = 1.0 / len(low_vol_assets)

        for asset in low_vol_assets:
            asset_idx = data.columns.get_loc(asset)
            full_weights[asset_idx] = weight_per_asset

        # Calculate metrics for fallback portfolio
        fallback_returns = returns[low_vol_assets].mean()
        fallback_cov = returns[low_vol_assets].cov()
        equal_weights = np.ones(len(low_vol_assets)) / len(low_vol_assets)

        port_return = np.dot(equal_weights, fallback_returns) * 252
        port_vol = np.sqrt(np.dot(equal_weights.T, np.dot(fallback_cov * 252, equal_weights)))
        sharpe_ratio = (port_return - risk_free_rate) / port_vol

        print(f"   ✅ Fallback portfolio created!")
        print(f"   📈 Expected Annual Return: {port_return:.2%}")
        print(f"   📊 Annual Volatility: {port_vol:.2%}")
        print(f"   ⚡ Sharpe Ratio: {sharpe_ratio:.3f}")

        return full_weights, {
            'return': port_return,
            'volatility': port_vol,
            'sharpe': sharpe_ratio,
            'success': True,
            'method': 'equal_weight_fallback',
            'valid_assets': len(low_vol_assets)
        }

    def simulate_future_performance(self, weights, data, weeks=10, initial_value=10000, num_simulations=1000):
        """Simulate future portfolio performance using Monte Carlo"""
        print(f"\n🔮 SIMULATING FUTURE PERFORMANCE")
        print("=" * 60)

        returns = data.pct_change().dropna()
        mean_returns = returns.mean()
        cov_matrix = returns.cov()

        # Portfolio daily return and volatility
        port_daily_return = np.dot(weights, mean_returns.values)
        port_daily_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix.values, weights)))

        # Simulation parameters
        days = weeks * 5  # Trading days

        print(f"   🎯 Simulating {num_simulations:,} scenarios for {weeks} weeks...")

        # Run Monte Carlo simulations
        simulations = []
        for _ in range(num_simulations):
            random_returns = np.random.normal(port_daily_return, port_daily_vol, days)
            portfolio_values = [initial_value]
            for daily_return in random_returns:
                portfolio_values.append(portfolio_values[-1] * (1 + daily_return))
            simulations.append(portfolio_values[1:])

        simulations = np.array(simulations)

        # Calculate statistics
        mean_simulation = np.mean(simulations, axis=0)
        percentile_5 = np.percentile(simulations, 5, axis=0)
        percentile_95 = np.percentile(simulations, 95, axis=0)

        # Create visualization
        future_dates = pd.date_range(start=datetime.now(), periods=days, freq='D')

        plt.figure(figsize=(14, 8))

        # Plot confidence intervals
        plt.fill_between(future_dates, percentile_5, percentile_95, alpha=0.2, color='lightblue', label='90% Confidence Interval')
        plt.plot(future_dates, mean_simulation, linewidth=3, color='darkblue', label='Expected Value')
        plt.axhline(y=initial_value, color='red', linestyle='--', alpha=0.7, label=f'Starting Value: ${initial_value:,}')

        plt.title(f'🌍 Global Portfolio Expected Value Simulation - Next {weeks} Weeks', fontsize=16, fontweight='bold')
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Portfolio Value ($)', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()

        # Add annotations
        final_expected = mean_simulation[-1]
        expected_return = (final_expected - initial_value) / initial_value * 100

        plt.annotate(f'Expected Final Value: ${final_expected:,.0f}\nExpected Return: {expected_return:+.1f}%',
                    xy=(future_dates[-1], final_expected),
                    xytext=(-120, 20), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.8),
                    fontsize=10, fontweight='bold')

        plt.show()

        # Print summary
        print(f"\n📊 SIMULATION RESULTS:")
        print(f"   🎯 Expected Final Value: ${final_expected:,.0f}")
        print(f"   📈 Expected Return: {expected_return:+.2f}%")
        print(f"   📊 5th Percentile: ${percentile_5[-1]:,.0f}")
        print(f"   📊 95th Percentile: ${percentile_95[-1]:,.0f}")
        print(f"   📊 Probability of Profit: {(simulations[:, -1] > initial_value).mean()*100:.1f}%")

        return {
            'expected_value': final_expected,
            'expected_return': expected_return,
            'percentile_5': percentile_5[-1],
            'percentile_95': percentile_95[-1],
            'probability_profit': (simulations[:, -1] > initial_value).mean()
        }

def main():
    """Main execution for global portfolio optimization"""
    print("🌍 GLOBAL PORTFOLIO OPTIMIZATION BOT")
    print("=" * 70)

    try:
        # Initialize optimizer
        optimizer = GlobalPortfolioOptimizer(
            max_tickers_per_index=50,  # Get more tickers for better diversification
            include_bonds=True
        )

        # Collect all tickers
        print("\n🎯 COLLECTING GLOBAL TICKERS...")
        all_tickers = optimizer.collect_all_tickers()

        # Fetch global data
        print(f"\n📊 FETCHING GLOBAL DATA...")
        data, failed = optimizer.fetch_global_data(sample_size=200)  # Increased sample size

        if data.empty or len(data.columns) < 20:
            print("❌ Insufficient global data for optimization")
            return None, None

        print(f"\n🎯 PROCEEDING WITH GLOBAL OPTIMIZATION")
        print(f"   📊 Assets: {len(data.columns)}")
        print(f"   📅 Data points: {len(data)}")
        print(f"   📈 Date range: {data.index[0]} to {data.index[-1]}")

        # Show asset breakdown by source
        asset_sources = {}
        for ticker in data.columns:
            source = optimizer.ticker_sources.get(ticker, "Unknown")
            asset_sources[source] = asset_sources.get(source, 0) + 1

        print(f"\n📊 ASSET BREAKDOWN:")
        for source, count in sorted(asset_sources.items(), key=lambda x: x[1], reverse=True):
            print(f"   {source}: {count} assets")

        # Optimize portfolio
        weights, metrics = optimizer.optimize_global_portfolio(data, max_weight=0.08)  # Max 8% per asset

        if weights is None or not metrics.get('success', False):
            print("❌ Portfolio optimization failed")
            return None, None, None, None

        # Show top holdings
        print(f"\n🏆 TOP GLOBAL HOLDINGS:")
        top_holdings = sorted(zip(data.columns, weights), key=lambda x: x[1], reverse=True)[:15]
        for i, (ticker, weight) in enumerate(top_holdings, 1):
            source = optimizer.ticker_sources.get(ticker, "Unknown")
            print(f"   {i:2d}. {ticker:8s} | {weight:6.2%} | {source}")

        # Run future simulation
        simulation_results = optimizer.simulate_future_performance(weights, data, weeks=10)

        print(f"\n🎉 GLOBAL OPTIMIZATION COMPLETE!")
        print(f"   🌍 Global diversification across {len(data.columns)} assets")
        print(f"   📊 Expected Annual Return: {metrics['return']:.2%}")
        print(f"   📈 Annual Volatility: {metrics['volatility']:.2%}")
        print(f"   ⚡ Sharpe Ratio: {metrics['sharpe']:.3f}")
        print(f"   🔮 10-Week Expected Return: {simulation_results['expected_return']:+.2f}%")

        return data, weights, metrics, simulation_results

    except Exception as e:
        print(f"❌ Error in global optimization: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None

if __name__ == "__main__":
    data, weights, metrics, simulation = main()
