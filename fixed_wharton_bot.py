# fixed_wharton_bot.py - Fixed version of the Wharton portfolio optimization bot
import yfinance as yf
import pandas as pd
import numpy as np
from scipy.optimize import minimize
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Extended list of ETFs for better diversification
etf_tickers = [
    'ARKK', 'GRID', 'FAN', 'PAVE', 'TAN', 'PHO', 'PBW', 'IBB', 'DGRO', 'ESGU',
    'SPY', 'QQQ', 'IWM', 'VTI', 'VEA', 'VWO', 'BND', 'TLT', 'GLD', 'SLV',
    'XLF', 'XLK', 'XLE', 'XLV', 'XLI', 'XLU', 'XLB', 'XLP', 'XLY', 'XLRE',
    'VGT', 'VHT', 'VFH', 'VNQ', 'VDE', 'VAW', 'VCR', 'VDC', 'VIS', 'VOX',
    'SCHB', 'SCHF', 'SCHE', 'SCHV', 'SCHA', 'SCHM', 'SCHG', 'SCHD', 'SCHX',
    'ITOT', 'IEFA', 'IEMG', 'AGG', 'IGSB', 'IGIB', 'IGLB', 'USIG', 'VCIT'
]

def fetch_yahoo_data(tickers, start_date=None, end_date=None):
    """Fetch data from Yahoo Finance with improved error handling"""
    from datetime import datetime, timedelta

    # Set default dates: 1 year back to today
    if end_date is None:
        end_date = datetime.now().date()  # Today's date
    if start_date is None:
        start_date = (datetime.now() - timedelta(days=365)).date()  # 1 year ago

    print(f"📊 Fetching data for {len(tickers)} ETFs from Yahoo Finance...")
    print(f"📅 Date range: {start_date} to {end_date}")

    # Use yfinance download for multiple tickers at once
    try:
        print("   🔄 Downloading all ETF data...")
        data = yf.download(tickers, start=start_date, end=end_date, progress=False)

        if data.empty:
            print("   ❌ No data downloaded")
            return pd.DataFrame(), tickers

        # Handle multi-level columns for multiple tickers
        if 'Close' in data.columns:
            close_data = data['Close']
        elif isinstance(data.columns, pd.MultiIndex):
            # Multi-ticker case
            close_data = data.xs('Close', level=1, axis=1)
        else:
            # Single ticker case
            close_data = data

        # Remove tickers with insufficient data
        failed = []
        valid_tickers = []

        for ticker in tickers:
            if ticker in close_data.columns:
                ticker_data = close_data[ticker].dropna()
                if len(ticker_data) > 50:  # Need at least 50 data points
                    valid_tickers.append(ticker)
                else:
                    failed.append(ticker)
            else:
                failed.append(ticker)

        # Keep only valid tickers
        if valid_tickers:
            final_data = close_data[valid_tickers]
            print(f"   ✅ Successfully processed {len(valid_tickers)} ETFs")
            print(f"   ❌ Failed/insufficient data: {len(failed)} ETFs")
            return final_data, failed
        else:
            print("   ❌ No valid ETF data")
            return pd.DataFrame(), tickers

    except Exception as e:
        print(f"   ❌ Error downloading data: {str(e)[:100]}")
        return pd.DataFrame(), tickers

def clean_data(data, min_data_points=100):
    """Clean and validate the data"""
    print(f"🧹 Cleaning data...")

    if data.empty:
        print("   ❌ No data to clean")
        return data

    initial_count = len(data.columns)

    # Remove ETFs with insufficient data (less than min_data_points non-null values)
    valid_columns = []
    for col in data.columns:
        non_null_count = data[col].count()
        if non_null_count >= min_data_points:
            valid_columns.append(col)

    if valid_columns:
        data = data[valid_columns]
    else:
        print(f"   ❌ No ETFs have sufficient data (need {min_data_points} points)")
        return pd.DataFrame()

    # Forward fill missing values (up to 5 days)
    data = data.fillna(method='ffill', limit=5)

    # Remove rows where all values are NaN
    data = data.dropna(how='all')

    final_count = len(data.columns)
    print(f"   📊 Kept {final_count}/{initial_count} ETFs with sufficient data")

    if not data.empty:
        print(f"   📅 Date range: {data.index[0]} to {data.index[-1]}")
        print(f"   📈 Data points: {len(data)}")

    return data

def calculate_portfolio_metrics(weights, mean_returns, cov_matrix, risk_free_rate=0.03):
    """Calculate portfolio return, volatility, and Sharpe ratio"""
    port_return = np.dot(weights, mean_returns) * 252  # Annualized
    port_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix * 252, weights)))  # Annualized
    sharpe_ratio = (port_return - risk_free_rate) / port_vol if port_vol > 0 else 0
    
    return port_return, port_vol, sharpe_ratio

def neg_sharpe(weights, mean_returns, cov_matrix, risk_free_rate):
    """Objective function: negative Sharpe ratio for minimization"""
    _, _, sharpe = calculate_portfolio_metrics(weights, mean_returns, cov_matrix, risk_free_rate)
    return -sharpe

def optimize_portfolio(mean_returns, cov_matrix, risk_free_rate=0.03, max_weight=0.2):
    """Optimize portfolio using mean-variance optimization"""
    print(f"🎯 Optimizing portfolio...")
    
    n_assets = len(mean_returns)
    
    # Initial guess (equal weights)
    weights_init = np.array([1/n_assets] * n_assets)
    
    # Constraints
    constraints = [
        {'type': 'eq', 'fun': lambda x: np.sum(x) - 1}  # Weights sum to 1
    ]
    
    # Bounds (0 to max_weight for each asset)
    bounds = tuple((0, max_weight) for _ in range(n_assets))
    
    try:
        # Run optimization
        result = minimize(
            neg_sharpe, 
            weights_init, 
            args=(mean_returns, cov_matrix, risk_free_rate),
            method='SLSQP', 
            bounds=bounds, 
            constraints=constraints,
            options={'maxiter': 1000}
        )
        
        if result.success:
            print("   ✅ Optimization successful")
            return result.x, True
        else:
            print(f"   ❌ Optimization failed: {result.message}")
            return weights_init, False
            
    except Exception as e:
        print(f"   ❌ Optimization error: {e}")
        return weights_init, False

def simulate_future_performance(weights, mean_returns, cov_matrix, weeks=10, initial_value=500000, num_simulations=1000):
    """Simulate portfolio performance for future weeks using Monte Carlo"""
    from datetime import datetime, timedelta
    import matplotlib.pyplot as plt

    print(f"🔮 Simulating portfolio performance for {weeks} weeks...")

    # Convert to daily parameters
    daily_return = mean_returns.values
    daily_cov = cov_matrix.values

    # Portfolio daily return and volatility
    port_daily_return = np.dot(weights, daily_return)
    port_daily_vol = np.sqrt(np.dot(weights.T, np.dot(daily_cov, weights)))

    # Simulation parameters
    days = weeks * 5  # Trading days (5 days per week)

    # Run Monte Carlo simulations
    simulations = []
    for _ in range(num_simulations):
        # Generate random returns
        random_returns = np.random.normal(port_daily_return, port_daily_vol, days)

        # Calculate cumulative portfolio value
        portfolio_values = [initial_value]
        for daily_return in random_returns:
            portfolio_values.append(portfolio_values[-1] * (1 + daily_return))

        simulations.append(portfolio_values[1:])  # Exclude initial value

    simulations = np.array(simulations)

    # Calculate statistics
    mean_simulation = np.mean(simulations, axis=0)
    percentile_5 = np.percentile(simulations, 5, axis=0)
    percentile_25 = np.percentile(simulations, 25, axis=0)
    percentile_75 = np.percentile(simulations, 75, axis=0)
    percentile_95 = np.percentile(simulations, 95, axis=0)

    # Create future dates
    start_date = datetime.now()
    future_dates = [start_date + timedelta(days=i) for i in range(1, days + 1)]

    # Create visualization
    plt.figure(figsize=(14, 8))

    # Plot confidence intervals
    plt.fill_between(future_dates, percentile_5, percentile_95, alpha=0.2, color='lightblue', label='90% Confidence Interval')
    plt.fill_between(future_dates, percentile_25, percentile_75, alpha=0.3, color='lightgreen', label='50% Confidence Interval')

    # Plot mean projection
    plt.plot(future_dates, mean_simulation, linewidth=3, color='darkblue', label='Expected Value')

    # Add current value line
    plt.axhline(y=initial_value, color='red', linestyle='--', alpha=0.7, label=f'Starting Value: ${initial_value:,}')

    plt.title(f'🔮 Portfolio Expected Value Simulation - Next {weeks} Weeks', fontsize=16, fontweight='bold')
    plt.xlabel('Date', fontsize=12)
    plt.ylabel('Portfolio Value ($)', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()

    # Add annotations
    final_expected = mean_simulation[-1]
    expected_return = (final_expected - initial_value) / initial_value * 100

    plt.annotate(f'Expected Final Value: ${final_expected:,.0f}\nExpected Return: {expected_return:+.1f}%',
                xy=(future_dates[-1], final_expected),
                xytext=(-120, 20), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.8),
                fontsize=10, fontweight='bold')

    plt.show()

    # Print summary statistics
    print(f"\n📊 SIMULATION RESULTS ({num_simulations:,} simulations):")
    print(f"   🎯 Expected Final Value: ${final_expected:,.0f}")
    print(f"   📈 Expected Return: {expected_return:+.2f}%")
    print(f"   📊 5th Percentile: ${percentile_5[-1]:,.0f}")
    print(f"   📊 95th Percentile: ${percentile_95[-1]:,.0f}")
    print(f"   📊 Probability of Profit: {(simulations[:, -1] > initial_value).mean()*100:.1f}%")

    return {
        'expected_value': final_expected,
        'expected_return': expected_return,
        'percentile_5': percentile_5[-1],
        'percentile_95': percentile_95[-1],
        'probability_profit': (simulations[:, -1] > initial_value).mean(),
        'simulations': simulations,
        'dates': future_dates
    }

def main():
    """Main execution function"""
    print("🚀 WHARTON PORTFOLIO OPTIMIZATION BOT")
    print("=" * 60)
    
    try:
        # Step 1: Fetch data
        raw_data, failed_tickers = fetch_yahoo_data(etf_tickers)
        
        if raw_data.empty:
            print("❌ No data fetched. Exiting.")
            return
        
        # Step 2: Clean data
        clean_data_df = clean_data(raw_data)
        
        if len(clean_data_df.columns) < 5:
            print("❌ Insufficient ETFs with good data. Exiting.")
            return
        
        # Step 3: Calculate returns and statistics
        print(f"📊 Calculating returns and statistics...")
        returns = clean_data_df.pct_change().dropna()
        mean_returns = returns.mean()
        cov_matrix = returns.cov()
        
        print(f"   📈 Average daily return: {mean_returns.mean():.4f}")
        print(f"   📊 Average daily volatility: {returns.std().mean():.4f}")
        
        # Step 4: Optimize portfolio
        RISK_FREE_RATE = 0.03
        MAX_WEIGHT = 0.15  # Max 15% in any single ETF
        
        optimal_weights, _ = optimize_portfolio(
            mean_returns, cov_matrix, RISK_FREE_RATE, MAX_WEIGHT
        )
        
        # Step 5: Display results
        print(f"\n📋 PORTFOLIO RESULTS")
        print("=" * 60)
        
        # Create portfolio dataframe
        portfolio_df = pd.DataFrame({
            'Ticker': clean_data_df.columns,
            'Weight': optimal_weights
        }).sort_values(by='Weight', ascending=False)
        
        # Filter to show only meaningful weights (>0.1%)
        significant_holdings = portfolio_df[portfolio_df['Weight'] > 0.001]
        
        print(f"🎯 OPTIMAL PORTFOLIO ({len(significant_holdings)} holdings):")
        print("-" * 40)
        for _, row in significant_holdings.iterrows():
            print(f"   {row['Ticker']:6} | {row['Weight']:6.2%}")
        
        # Calculate portfolio metrics
        port_return, port_vol, sharpe = calculate_portfolio_metrics(
            optimal_weights, mean_returns, cov_matrix, RISK_FREE_RATE
        )
        
        print(f"\n📊 PORTFOLIO METRICS:")
        print(f"   Expected Annual Return: {port_return:.2%}")
        print(f"   Annual Volatility: {port_vol:.2%}")
        print(f"   Sharpe Ratio: {sharpe:.3f}")
        print(f"   Risk-Free Rate: {RISK_FREE_RATE:.1%}")
        
        # Run future simulation
        print(f"\n🔮 FUTURE PERFORMANCE SIMULATION")
        print("=" * 60)
        simulation_results = simulate_future_performance(
            optimal_weights, mean_returns, cov_matrix, weeks=10, initial_value=10000
        )

        if failed_tickers:
            print(f"\n⚠️  Failed to fetch data for: {', '.join(failed_tickers[:10])}")
            if len(failed_tickers) > 10:
                print(f"   ... and {len(failed_tickers) - 10} more")

        return portfolio_df, {
            'return': port_return,
            'volatility': port_vol,
            'sharpe': sharpe,
            'simulation': simulation_results
        }
        
    except Exception as e:
        print(f"❌ Critical error: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    portfolio, metrics = main()
