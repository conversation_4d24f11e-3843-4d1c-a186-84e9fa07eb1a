# trade_limited_optimizer.py - Portfolio Optimizer with 200 Trade Limit
"""
Advanced Portfolio Optimizer that maximizes profit with a 200-trade constraint.
Simulates different trading strategies to find optimal trade sequences.
Starting Capital: $500,000
Trade Limit: 200 trades total
Risk Allocation: 65% High Risk / 35% Low Risk
"""

import yfinance as yf
import pandas as pd
import numpy as np
from scipy.optimize import minimize
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import random
import warnings
warnings.filterwarnings('ignore')

class TradeLimitedOptimizer:
    """Portfolio optimizer with 200-trade limit constraint"""
    
    def __init__(self, initial_capital=500000, max_trades=200, high_risk_pct=0.65, low_risk_pct=0.35):
        self.initial_capital = initial_capital
        self.max_trades = max_trades
        self.high_risk_pct = high_risk_pct
        self.low_risk_pct = low_risk_pct
        self.best_strategy = None
        self.best_profit = -999999
        self.trade_strategies = []
        
        print(f"🎯 TRADE-LIMITED PORTFOLIO OPTIMIZER")
        print(f"   💰 Starting Capital: ${initial_capital:,}")
        print(f"   🔄 Maximum Trades: {max_trades}")
        print(f"   📈 High Risk: {high_risk_pct:.0%} | 📊 Low Risk: {low_risk_pct:.0%}")
    
    def get_trading_universe(self):
        """Get high-profit potential trading assets"""
        print("\n🌍 Building Trading Universe...")
        
        # High volatility, high profit potential assets
        high_profit_assets = {
            "Momentum_Stocks": ["TSLA", "NVDA", "AMD", "PLTR", "NET", "SNOW", "CRWD", "ZS"],
            "Growth_ETFs": ["ARKK", "QQQ", "TQQQ", "SOXL", "TECL", "SPXL", "UPRO"],
            "Crypto_Exposure": ["BITO", "GBTC", "COIN", "MSTR", "RIOT", "MARA", "HUT"],
            "Emerging_Markets": ["BABA", "JD", "PDD", "NIO", "XPEV", "LI", "KWEB", "FXI"],
            "Commodities": ["GLD", "SLV", "USO", "UNG", "DJP", "PDBC", "XLE", "XOP"],
            "Biotech": ["MRNA", "BNTX", "GILD", "BIIB", "XBI", "IBB", "ARKG"],
            "Small_Cap": ["IWM", "VTWO", "IJR", "VB", "SCHA"]
        }
        
        # Stable, lower-risk assets for balance
        stable_assets = {
            "Treasury_Bills": ["SHY", "BIL", "SHV", "TBIL", "SGOV"],
            "Corporate_Bonds": ["LQD", "VCIT", "VCSH", "AGG", "BND"],
            "Dividend_ETFs": ["VYM", "SCHD", "DVY", "VIG", "DGRO"],
            "Money_Market": ["MINT", "JPST", "GSY", "NEAR", "FLOT"],
            "International": ["VEA", "EFA", "VGK", "ACWI", "VXUS"]
        }
        
        self.high_profit_universe = []
        self.stable_universe = []
        
        for category, tickers in high_profit_assets.items():
            self.high_profit_universe.extend(tickers)
            print(f"   📈 {category}: {len(tickers)} assets")
        
        for category, tickers in stable_assets.items():
            self.stable_universe.extend(tickers)
            print(f"   📊 {category}: {len(tickers)} assets")
        
        # Remove duplicates
        self.high_profit_universe = list(set(self.high_profit_universe))
        self.stable_universe = list(set(self.stable_universe))
        
        print(f"   🎯 High Profit Universe: {len(self.high_profit_universe)} assets")
        print(f"   🎯 Stable Universe: {len(self.stable_universe)} assets")
        
        return self.high_profit_universe, self.stable_universe
    
    def fetch_trading_data(self, tickers):
        """Fetch data optimized for trading analysis"""
        end_date = datetime.now().date()
        start_date = (datetime.now() - timedelta(days=365)).date()
        
        try:
            print(f"   📥 Fetching trading data for {len(tickers)} assets...")
            data = yf.download(tickers, start=start_date, end=end_date, progress=False)
            
            if data.empty:
                return pd.DataFrame()
            
            # Handle multi-level columns
            if 'Close' in data.columns:
                close_data = data['Close']
            elif isinstance(data.columns, pd.MultiIndex):
                close_data = data.xs('Close', level=1, axis=1)
            else:
                close_data = data
            
            # Clean and validate data
            valid_tickers = []
            for ticker in tickers:
                if ticker in close_data.columns:
                    ticker_data = close_data[ticker].dropna()
                    if len(ticker_data) > 100:  # Sufficient data
                        valid_tickers.append(ticker)
            
            if valid_tickers:
                final_data = close_data[valid_tickers].fillna(method='ffill').dropna()
                print(f"   ✅ Valid trading data for {len(valid_tickers)} assets")
                return final_data
            else:
                return pd.DataFrame()
                
        except Exception as e:
            print(f"   ❌ Error fetching data: {e}")
            return pd.DataFrame()
    
    def calculate_trading_metrics(self, data):
        """Calculate trading-specific metrics for profit optimization"""
        returns = data.pct_change().dropna()
        
        metrics = {}
        for ticker in data.columns:
            ticker_returns = returns[ticker].dropna()
            
            # Trading metrics
            daily_vol = ticker_returns.std()
            mean_return = ticker_returns.mean()
            sharpe = (mean_return * 252 - 0.03) / (daily_vol * np.sqrt(252)) if daily_vol > 0 else 0
            
            # Profit potential metrics
            max_daily_gain = ticker_returns.max()
            max_daily_loss = ticker_returns.min()
            profit_ratio = abs(max_daily_gain / max_daily_loss) if max_daily_loss < 0 else 1
            
            # Momentum indicators
            recent_returns = ticker_returns.tail(20)
            momentum = recent_returns.mean()
            
            metrics[ticker] = {
                'daily_vol': daily_vol,
                'mean_return': mean_return,
                'sharpe': sharpe,
                'max_gain': max_daily_gain,
                'max_loss': max_daily_loss,
                'profit_ratio': profit_ratio,
                'momentum': momentum,
                'annual_return': mean_return * 252
            }
        
        return metrics
    
    def generate_trading_strategy(self, high_profit_data, stable_data, strategy_type="balanced"):
        """Generate different trading strategies within 200-trade limit"""
        
        if high_profit_data.empty or stable_data.empty:
            return None
        
        # Calculate metrics for asset selection
        hp_metrics = self.calculate_trading_metrics(high_profit_data)
        stable_metrics = self.calculate_trading_metrics(stable_data)
        
        # Strategy parameters based on type
        if strategy_type == "aggressive":
            hp_allocation = 0.80  # 80% high profit
            stable_allocation = 0.20
            max_positions = 15
        elif strategy_type == "conservative":
            hp_allocation = 0.50  # 50% high profit
            stable_allocation = 0.50
            max_positions = 20
        else:  # balanced
            hp_allocation = self.high_risk_pct
            stable_allocation = self.low_risk_pct
            max_positions = 18
        
        # Select best assets based on profit potential
        hp_sorted = sorted(hp_metrics.items(), 
                          key=lambda x: x[1]['sharpe'] * x[1]['profit_ratio'], reverse=True)
        stable_sorted = sorted(stable_metrics.items(), 
                              key=lambda x: x[1]['sharpe'], reverse=True)
        
        # Calculate optimal number of positions to stay under trade limit
        hp_positions = min(int(max_positions * hp_allocation / (hp_allocation + stable_allocation)), 
                          len(hp_sorted))
        stable_positions = min(max_positions - hp_positions, len(stable_sorted))
        
        # Select top assets
        selected_hp = [ticker for ticker, _ in hp_sorted[:hp_positions]]
        selected_stable = [ticker for ticker, _ in stable_sorted[:stable_positions]]
        
        # Create combined dataset
        all_selected = selected_hp + selected_stable
        combined_data = pd.concat([
            high_profit_data[selected_hp], 
            stable_data[selected_stable]
        ], axis=1)
        
        # Optimize weights
        weights = self.optimize_for_profit(combined_data, selected_hp, selected_stable, 
                                         hp_allocation, stable_allocation)
        
        if weights is not None:
            # Calculate expected trades (initial buys + potential rebalancing)
            initial_trades = len(all_selected)  # Initial position entries
            rebalancing_trades = min(50, self.max_trades - initial_trades)  # Reserve for rebalancing
            
            strategy = {
                'type': strategy_type,
                'tickers': all_selected,
                'hp_tickers': selected_hp,
                'stable_tickers': selected_stable,
                'weights': weights,
                'combined_data': combined_data,
                'initial_trades': initial_trades,
                'rebalancing_trades': rebalancing_trades,
                'total_estimated_trades': initial_trades + rebalancing_trades,
                'hp_allocation': hp_allocation,
                'stable_allocation': stable_allocation
            }
            
            return strategy
        
        return None
    
    def optimize_for_profit(self, data, hp_tickers, stable_tickers, hp_alloc, stable_alloc):
        """Optimize portfolio for maximum profit with risk constraints"""
        returns = data.pct_change().dropna()
        mean_returns = returns.mean()
        cov_matrix = returns.cov() + np.eye(len(returns.columns)) * 1e-6
        
        def profit_objective(weights):
            # Maximize expected return with penalty for excessive risk
            port_return = np.dot(weights, mean_returns) * 252
            port_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix * 252, weights)))
            
            # Profit-focused objective with risk penalty
            profit_score = port_return - 0.5 * port_vol  # Risk-adjusted profit
            return -profit_score  # Minimize negative profit
        
        n_assets = len(data.columns)
        
        # Risk allocation constraints
        def risk_constraint(weights):
            hp_weight_sum = sum(weights[i] for i, ticker in enumerate(data.columns) 
                               if ticker in hp_tickers)
            return hp_weight_sum - hp_alloc
        
        def stable_constraint(weights):
            stable_weight_sum = sum(weights[i] for i, ticker in enumerate(data.columns) 
                                   if ticker in stable_tickers)
            return stable_weight_sum - stable_alloc
        
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # Weights sum to 1
            {'type': 'eq', 'fun': risk_constraint},  # High profit allocation
            {'type': 'eq', 'fun': stable_constraint}  # Stable allocation
        ]
        
        bounds = tuple((0, 0.25) for _ in range(n_assets))  # Max 25% per asset
        initial_weights = np.ones(n_assets) / n_assets
        
        try:
            result = minimize(profit_objective, initial_weights, method='SLSQP',
                            bounds=bounds, constraints=constraints,
                            options={'maxiter': 1000})
            
            if result.success:
                return result.x
            else:
                # Fallback: equal weights within risk buckets
                weights = np.zeros(n_assets)
                hp_count = len(hp_tickers)
                stable_count = len(stable_tickers)
                
                for i, ticker in enumerate(data.columns):
                    if ticker in hp_tickers:
                        weights[i] = hp_alloc / hp_count
                    else:
                        weights[i] = stable_alloc / stable_count
                
                return weights
                
        except Exception as e:
            print(f"   ❌ Optimization error: {e}")
            return None
    
    def simulate_trading_strategies(self, num_strategies=50):
        """Simulate multiple trading strategies to find optimal approach"""
        print(f"\n🔄 SIMULATING {num_strategies} TRADING STRATEGIES")
        print("=" * 70)
        
        # Fetch data
        hp_data = self.fetch_trading_data(self.high_profit_universe)
        stable_data = self.fetch_trading_data(self.stable_universe)
        
        if hp_data.empty or stable_data.empty:
            print("❌ Insufficient trading data")
            return None
        
        print(f"   📈 High Profit Assets: {len(hp_data.columns)}")
        print(f"   📊 Stable Assets: {len(stable_data.columns)}")
        
        strategies = []
        strategy_types = ["aggressive", "balanced", "conservative"]
        
        for i in range(num_strategies):
            strategy_type = random.choice(strategy_types)
            
            strategy = self.generate_trading_strategy(hp_data, stable_data, strategy_type)
            
            if strategy and strategy['total_estimated_trades'] <= self.max_trades:
                # Calculate strategy performance
                performance = self.evaluate_strategy_performance(strategy)
                
                if performance:
                    strategy.update(performance)
                    strategies.append(strategy)
                    
                    # Update best strategy
                    if performance['total_profit'] > self.best_profit:
                        self.best_profit = performance['total_profit']
                        self.best_strategy = strategy
            
            # Progress update
            if (i + 1) % 10 == 0:
                print(f"   🔄 Strategy {i + 1}/{num_strategies} | Best Profit: ${self.best_profit:,.0f}")
        
        # Sort strategies by profit
        strategies.sort(key=lambda x: x['total_profit'], reverse=True)
        self.trade_strategies = strategies[:10]  # Keep top 10
        
        print(f"\n✅ Completed {num_strategies} strategy simulations")
        print(f"   🏆 Best Total Profit: ${self.best_profit:,.0f}")
        print(f"   📊 Valid strategies found: {len(strategies)}")
        
        return self.best_strategy
    
    def evaluate_strategy_performance(self, strategy):
        """Evaluate strategy performance and profit potential"""
        try:
            data = strategy['combined_data']
            weights = strategy['weights']
            
            returns = data.pct_change().dropna()
            mean_returns = returns.mean()
            cov_matrix = returns.cov()
            
            # Portfolio metrics
            port_return = np.dot(weights, mean_returns) * 252
            port_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix * 252, weights)))
            sharpe = (port_return - 0.03) / port_vol if port_vol > 0 else 0
            
            # Profit calculations
            expected_annual_profit = self.initial_capital * port_return
            total_profit = expected_annual_profit - (strategy['total_estimated_trades'] * 10)  # $10 per trade cost
            
            # Risk metrics
            portfolio_returns = (returns * weights).sum(axis=1)
            max_drawdown = (portfolio_returns.cumsum() - portfolio_returns.cumsum().expanding().max()).min()
            
            return {
                'annual_return': port_return,
                'volatility': port_vol,
                'sharpe_ratio': sharpe,
                'expected_annual_profit': expected_annual_profit,
                'trading_costs': strategy['total_estimated_trades'] * 10,
                'total_profit': total_profit,
                'max_drawdown': max_drawdown,
                'profit_per_trade': total_profit / strategy['total_estimated_trades']
            }
            
        except Exception as e:
            print(f"   ❌ Strategy evaluation error: {e}")
            return None

    def display_optimal_trading_strategy(self):
        """Display the optimal trading strategy with detailed trade plan"""
        if not self.best_strategy:
            print("❌ No optimal trading strategy found")
            return

        strategy = self.best_strategy
        tickers = strategy['tickers']
        weights = strategy['weights']

        print(f"\n🏆 OPTIMAL TRADING STRATEGY FOUND!")
        print("=" * 70)
        print(f"   🎯 Strategy Type: {strategy['type'].title()}")
        print(f"   📊 Total Positions: {len(tickers)}")
        print(f"   📈 High Profit Assets: {len(strategy['hp_tickers'])}")
        print(f"   📊 Stable Assets: {len(strategy['stable_tickers'])}")
        print(f"   🔄 Estimated Total Trades: {strategy['total_estimated_trades']}/{self.max_trades}")
        print(f"   📈 Expected Annual Return: {strategy['annual_return']:.2%}")
        print(f"   💰 Expected Annual Profit: ${strategy['expected_annual_profit']:,.0f}")
        print(f"   💸 Trading Costs: ${strategy['trading_costs']:,.0f}")
        print(f"   🏆 Net Profit: ${strategy['total_profit']:,.0f}")
        print(f"   ⚡ Sharpe Ratio: {strategy['sharpe_ratio']:.3f}")

        print(f"\n💰 DETAILED TRADE EXECUTION PLAN (${self.initial_capital:,} total):")
        print("=" * 80)

        # Sort positions by weight
        trade_plan = list(zip(tickers, weights))
        trade_plan.sort(key=lambda x: x[1], reverse=True)

        print(f"   {'Trade':<5} {'Ticker':<8} {'Weight':<8} {'Amount':<12} {'Type':<12} {'Category'}")
        print(f"   {'-'*5} {'-'*8} {'-'*8} {'-'*12} {'-'*12} {'-'*20}")

        trade_num = 1
        total_hp_amount = 0
        total_stable_amount = 0

        for ticker, weight in trade_plan:
            if weight > 0.005:  # Show positions > 0.5%
                amount = weight * self.initial_capital
                asset_type = "HIGH PROFIT" if ticker in strategy['hp_tickers'] else "STABLE"
                type_emoji = "📈" if ticker in strategy['hp_tickers'] else "📊"

                # Categorize asset
                category = self.categorize_asset(ticker)

                if ticker in strategy['hp_tickers']:
                    total_hp_amount += amount
                else:
                    total_stable_amount += amount

                print(f"   {trade_num:<5} {ticker:<8} {weight:>7.2%} ${amount:>10,.0f} {type_emoji} {asset_type:<10} {category}")
                trade_num += 1

        print(f"\n📊 ALLOCATION VERIFICATION:")
        print("=" * 40)
        print(f"   📈 High Profit Total: ${total_hp_amount:>12,.0f} ({total_hp_amount/self.initial_capital:.1%})")
        print(f"   📊 Stable Total:      ${total_stable_amount:>12,.0f} ({total_stable_amount/self.initial_capital:.1%})")
        print(f"   💰 Grand Total:       ${total_hp_amount + total_stable_amount:>12,.0f}")

        print(f"\n🔄 TRADE EXECUTION SEQUENCE:")
        print("=" * 50)
        print(f"   1. 📋 Initial Portfolio Setup: {len([w for w in weights if w > 0.005])} trades")
        print(f"   2. 🔄 Quarterly Rebalancing: ~{strategy['rebalancing_trades']//4} trades per quarter")
        print(f"   3. 📊 Total Trade Budget: {strategy['total_estimated_trades']}/{self.max_trades} trades")
        print(f"   4. 💸 Trading Cost Budget: ${strategy['trading_costs']:,.0f} (${strategy['trading_costs']/strategy['total_estimated_trades']:.0f} per trade)")

        return trade_plan

    def categorize_asset(self, ticker):
        """Categorize asset for display purposes"""
        categories = {
            "TSLA": "Electric Vehicles", "NVDA": "AI/Semiconductors", "AMD": "Semiconductors",
            "PLTR": "Data Analytics", "NET": "Cloud Security", "SNOW": "Cloud Computing",
            "CRWD": "Cybersecurity", "ZS": "Cloud Security", "ARKK": "Innovation ETF",
            "QQQ": "Tech ETF", "TQQQ": "3x Tech ETF", "SOXL": "3x Semiconductor",
            "TECL": "3x Technology", "SPXL": "3x S&P 500", "UPRO": "3x S&P 500",
            "BITO": "Bitcoin ETF", "GBTC": "Bitcoin Trust", "COIN": "Crypto Exchange",
            "MSTR": "Bitcoin Treasury", "RIOT": "Bitcoin Mining", "MARA": "Bitcoin Mining",
            "HUT": "Bitcoin Mining", "BABA": "Chinese E-commerce", "JD": "Chinese E-commerce",
            "PDD": "Chinese E-commerce", "NIO": "Chinese EV", "XPEV": "Chinese EV",
            "LI": "Chinese EV", "KWEB": "Chinese Internet", "FXI": "China ETF",
            "GLD": "Gold ETF", "SLV": "Silver ETF", "USO": "Oil ETF", "UNG": "Natural Gas",
            "DJP": "Commodities", "PDBC": "Commodities", "XLE": "Energy Sector",
            "XOP": "Oil & Gas", "MRNA": "mRNA Vaccines", "BNTX": "mRNA Vaccines",
            "GILD": "Biotech", "BIIB": "Biotech", "XBI": "Biotech ETF", "IBB": "Biotech ETF",
            "ARKG": "Genomics ETF", "IWM": "Small Cap ETF", "VTWO": "Small Cap ETF",
            "SHY": "Short Treasury", "BIL": "Treasury Bills", "SHV": "Short Treasury",
            "TBIL": "Treasury Bills", "SGOV": "Treasury Bills", "LQD": "Corporate Bonds",
            "VCIT": "Corporate Bonds", "VCSH": "Short Corporate", "AGG": "Aggregate Bonds",
            "BND": "Total Bond", "VYM": "High Dividend", "SCHD": "Dividend Growth",
            "DVY": "Dividend ETF", "VIG": "Dividend Growth", "DGRO": "Dividend Growth",
            "MINT": "Money Market", "JPST": "Money Market", "GSY": "Money Market",
            "NEAR": "Money Market", "FLOT": "Floating Rate", "VEA": "Developed Markets",
            "EFA": "International", "VGK": "European Stocks", "ACWI": "Global Stocks",
            "VXUS": "International"
        }
        return categories.get(ticker, "Other")

    def create_profit_visualization(self, weeks=10):
        """Create comprehensive profit visualization"""
        if not self.best_strategy:
            return None

        print(f"\n🔮 PROFIT SIMULATION & VISUALIZATION")
        print("=" * 60)

        strategy = self.best_strategy
        data = strategy['combined_data']
        weights = strategy['weights']

        returns = data.pct_change().dropna()
        mean_returns = returns.mean()
        cov_matrix = returns.cov()

        # Portfolio metrics
        port_daily_return = np.dot(weights, mean_returns.values)
        port_daily_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix.values, weights)))

        # Monte Carlo simulation for profit projection
        days = weeks * 5
        simulations = []

        print(f"   🎯 Running 1,000 profit simulations for {weeks} weeks...")

        for _ in range(1000):
            random_returns = np.random.normal(port_daily_return, port_daily_vol, days)
            portfolio_values = [self.initial_capital]
            for daily_return in random_returns:
                portfolio_values.append(portfolio_values[-1] * (1 + daily_return))
            simulations.append(portfolio_values[1:])

        simulations = np.array(simulations)

        # Calculate profit statistics
        final_values = simulations[:, -1]
        profits = final_values - self.initial_capital

        mean_profit = np.mean(profits)
        median_profit = np.median(profits)
        profit_5th = np.percentile(profits, 5)
        profit_95th = np.percentile(profits, 95)
        profit_probability = (profits > 0).mean() * 100

        # Create visualization
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 1. Profit Distribution
        ax1.hist(profits, bins=50, alpha=0.7, color='green', edgecolor='black')
        ax1.axvline(mean_profit, color='red', linestyle='--', linewidth=2, label=f'Mean: ${mean_profit:,.0f}')
        ax1.axvline(median_profit, color='blue', linestyle='--', linewidth=2, label=f'Median: ${median_profit:,.0f}')
        ax1.axvline(0, color='black', linestyle='-', alpha=0.5, label='Break-even')
        ax1.set_title(f'💰 Profit Distribution ({weeks} weeks)', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Profit ($)')
        ax1.set_ylabel('Frequency')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. Portfolio Value Evolution
        future_dates = pd.date_range(start=datetime.now(), periods=days, freq='D')
        mean_simulation = np.mean(simulations, axis=0)
        percentile_5 = np.percentile(simulations, 5, axis=0)
        percentile_95 = np.percentile(simulations, 95, axis=0)

        ax2.fill_between(future_dates, percentile_5, percentile_95, alpha=0.2, color='lightblue', label='90% Confidence')
        ax2.plot(future_dates, mean_simulation, linewidth=3, color='darkblue', label='Expected Value')
        ax2.axhline(y=self.initial_capital, color='red', linestyle='--', alpha=0.7, label=f'Starting: ${self.initial_capital:,}')

        ax2.set_title(f'📈 Portfolio Value Projection', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Date')
        ax2.set_ylabel('Portfolio Value ($)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)

        # 3. Trade Allocation Pie Chart
        hp_allocation = strategy['hp_allocation']
        stable_allocation = strategy['stable_allocation']

        ax3.pie([hp_allocation, stable_allocation],
                labels=[f'High Profit ({hp_allocation:.0%})', f'Stable ({stable_allocation:.0%})'],
                colors=['red', 'green'], autopct='%1.1f%%', startangle=90)
        ax3.set_title('🎯 Capital Allocation', fontsize=14, fontweight='bold')

        # 4. Strategy Metrics
        ax4.axis('off')

        annual_return = port_daily_return * 252
        annual_vol = port_daily_vol * np.sqrt(252)

        metrics_text = f"""
💰 PROFIT-OPTIMIZED STRATEGY METRICS

💵 Starting Capital: ${self.initial_capital:,}
🔄 Trade Limit: {self.max_trades} trades
📊 Strategy Type: {strategy['type'].title()}

📈 PERFORMANCE METRICS:
💰 Expected Annual Profit: ${strategy['expected_annual_profit']:,.0f}
📈 Annual Return: {annual_return:.2%}
📊 Volatility: {annual_vol:.2%}
⚡ Sharpe Ratio: {strategy['sharpe_ratio']:.3f}

🔄 TRADING METRICS:
📊 Total Positions: {len(strategy['tickers'])}
🔄 Estimated Trades: {strategy['total_estimated_trades']}
💸 Trading Costs: ${strategy['trading_costs']:,.0f}
💰 Net Profit: ${strategy['total_profit']:,.0f}
📈 Profit per Trade: ${strategy['profit_per_trade']:,.0f}

🔮 {weeks}-WEEK PROJECTION:
💰 Expected Profit: ${mean_profit:,.0f}
📊 Profit Range: ${profit_5th:,.0f} to ${profit_95th:,.0f}
📈 Profit Probability: {profit_probability:.1f}%
"""

        ax4.text(0.05, 0.95, metrics_text, transform=ax4.transAxes, fontsize=10,
                 verticalalignment='top', fontfamily='monospace',
                 bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))

        plt.tight_layout()
        plt.suptitle('🎯 TRADE-LIMITED PROFIT OPTIMIZATION RESULTS',
                     fontsize=18, fontweight='bold', y=0.98)
        plt.show()

        print(f"\n📊 PROFIT SIMULATION RESULTS:")
        print(f"   💰 Expected Profit: ${mean_profit:,.0f}")
        print(f"   📊 Profit Range (90%): ${profit_5th:,.0f} to ${profit_95th:,.0f}")
        print(f"   📈 Probability of Profit: {profit_probability:.1f}%")
        print(f"   🏆 Best Case Profit: ${profit_95th:,.0f}")
        print(f"   📊 Worst Case Loss: ${profit_5th:,.0f}")

        return {
            'expected_profit': mean_profit,
            'profit_probability': profit_probability,
            'profit_range': (profit_5th, profit_95th),
            'annual_return': annual_return,
            'trading_costs': strategy['trading_costs'],
            'net_profit': strategy['total_profit']
        }

def print_final_trading_plan(optimizer):
    """Print comprehensive trading plan with exact instructions"""
    if not optimizer.best_strategy:
        return

    strategy = optimizer.best_strategy

    print(f"\n" + "="*80)
    print(f"🎯 FINAL TRADING PLAN - PROFIT MAXIMIZATION WITH 200 TRADE LIMIT")
    print(f"="*80)

    print(f"\n💰 CAPITAL & TRADE ALLOCATION:")
    print(f"   💵 Total Investment Capital: ${optimizer.initial_capital:,}")
    print(f"   🔄 Maximum Trades Available: {optimizer.max_trades}")
    print(f"   📊 Estimated Trades Used: {strategy['total_estimated_trades']}")
    print(f"   🔄 Remaining Trade Capacity: {optimizer.max_trades - strategy['total_estimated_trades']}")
    print(f"   💸 Total Trading Costs: ${strategy['trading_costs']:,.0f}")

    print(f"\n📈 PROFIT OPTIMIZATION RESULTS:")
    print(f"   🏆 Strategy Type: {strategy['type'].title()}")
    print(f"   💰 Expected Annual Profit: ${strategy['expected_annual_profit']:,.0f}")
    print(f"   📈 Expected Annual Return: {strategy['annual_return']:.2%}")
    print(f"   💰 Net Profit (After Costs): ${strategy['total_profit']:,.0f}")
    print(f"   📊 Profit per Trade: ${strategy['profit_per_trade']:,.0f}")
    print(f"   ⚡ Sharpe Ratio: {strategy['sharpe_ratio']:.3f}")

    print(f"\n🎯 EXACT TRADING INSTRUCTIONS:")
    print("=" * 60)

    # Get trade plan
    tickers = strategy['tickers']
    weights = strategy['weights']
    trade_plan = list(zip(tickers, weights))
    trade_plan.sort(key=lambda x: x[1], reverse=True)

    print(f"\n📋 PHASE 1: INITIAL PORTFOLIO SETUP")
    print(f"   Execute the following {len([w for w in weights if w > 0.005])} trades to establish positions:")
    print(f"\n   {'Order':<5} {'Ticker':<8} {'Amount':<12} {'Weight':<8} {'Type':<12} {'Purpose'}")
    print(f"   {'-'*5} {'-'*8} {'-'*12} {'-'*8} {'-'*12} {'-'*30}")

    order_num = 1
    for ticker, weight in trade_plan:
        if weight > 0.005:  # Show significant positions
            amount = weight * optimizer.initial_capital
            asset_type = "HIGH PROFIT" if ticker in strategy['hp_tickers'] else "STABLE"
            type_emoji = "📈" if ticker in strategy['hp_tickers'] else "📊"
            purpose = optimizer.categorize_asset(ticker)

            print(f"   {order_num:<5} {ticker:<8} ${amount:>10,.0f} {weight:>7.2%} {type_emoji} {asset_type:<10} {purpose}")
            order_num += 1

    print(f"\n📊 PHASE 2: REBALANCING STRATEGY")
    print(f"   Reserve {strategy['rebalancing_trades']} trades for quarterly rebalancing:")
    print(f"   • Q1 Rebalancing: ~{strategy['rebalancing_trades']//4} trades")
    print(f"   • Q2 Rebalancing: ~{strategy['rebalancing_trades']//4} trades")
    print(f"   • Q3 Rebalancing: ~{strategy['rebalancing_trades']//4} trades")
    print(f"   • Q4 Rebalancing: ~{strategy['rebalancing_trades']//4} trades")

    print(f"\n🎯 RISK MANAGEMENT:")
    print(f"   📈 High Profit Allocation: {strategy['hp_allocation']:.0%} (${optimizer.initial_capital * strategy['hp_allocation']:,.0f})")
    print(f"   📊 Stable Allocation: {strategy['stable_allocation']:.0%} (${optimizer.initial_capital * strategy['stable_allocation']:,.0f})")
    print(f"   🛡️ Maximum Position Size: 25% per asset")
    print(f"   📊 Diversification: {len(strategy['tickers'])} different assets")

    print(f"\n💡 EXECUTION RECOMMENDATIONS:")
    print(f"   1. 🏦 Ensure sufficient margin/buying power in brokerage account")
    print(f"   2. 📊 Execute initial trades during market hours for best pricing")
    print(f"   3. 🔄 Set up quarterly calendar reminders for rebalancing")
    print(f"   4. 📈 Monitor high-profit positions more frequently (weekly)")
    print(f"   5. 📊 Review stable positions monthly")
    print(f"   6. 💸 Track trading costs to stay within ${strategy['trading_costs']:,.0f} budget")

    print(f"\n🔮 EXPECTED OUTCOMES:")
    print(f"   💰 Target Annual Profit: ${strategy['expected_annual_profit']:,.0f}")
    print(f"   📈 Expected Portfolio Growth: {strategy['annual_return']:.1%}")
    print(f"   🎯 Profit Efficiency: ${strategy['profit_per_trade']:,.0f} per trade")
    print(f"   📊 Risk-Adjusted Return: {strategy['sharpe_ratio']:.3f} Sharpe ratio")

def main():
    """Main execution for trade-limited profit optimization"""
    print("🎯 TRADE-LIMITED PROFIT OPTIMIZER - MAXIMIZING RETURNS WITH 200 TRADES")
    print("=" * 85)

    # Initialize optimizer
    optimizer = TradeLimitedOptimizer(
        initial_capital=500000,
        max_trades=200,
        high_risk_pct=0.65,
        low_risk_pct=0.35
    )

    try:
        # Build trading universe
        _ = optimizer.get_trading_universe()

        # Simulate trading strategies
        best_strategy = optimizer.simulate_trading_strategies(num_strategies=75)

        if best_strategy:
            # Display optimal strategy
            trade_plan = optimizer.display_optimal_trading_strategy()

            # Create profit visualization
            simulation_results = optimizer.create_profit_visualization(weeks=10)

            # Print comprehensive trading plan
            print_final_trading_plan(optimizer)

            print(f"\n🎉 TRADE-LIMITED OPTIMIZATION COMPLETE!")
            print(f"   🏆 Maximum Profit Strategy Found: ${optimizer.best_profit:,.0f}")
            print(f"   🔄 Trade Efficiency: {optimizer.best_strategy['total_estimated_trades']}/{optimizer.max_trades} trades used")
            print(f"   📈 Annual Return Target: {optimizer.best_strategy['annual_return']:.1%}")
            print(f"   💰 Ready to execute with ${optimizer.initial_capital:,} capital")
            print(f"   🎯 Profit-optimized portfolio with strict trade limits")

            return optimizer.best_strategy, trade_plan, simulation_results
        else:
            print("❌ No profitable trading strategy found within trade limits")
            return None, None, None

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

if __name__ == "__main__":
    strategy, trades, results = main()
