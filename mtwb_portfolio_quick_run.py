#!/usr/bin/env python3
"""
Quick run version of <PERSON>'s MTWB Portfolio Optimizer
Runs without matplotlib visualization for faster completion
"""

import yfinance as yf
import pandas as pd
import numpy as np
from scipy.optimize import minimize
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class MTWBPortfolioOptimizer:
    """Make the World Better Foundation Portfolio Optimizer"""
    
    def __init__(self, initial_capital=500000, target_capital=1500000, years_to_target=10, annual_drawdown=10000, drawdown_start_year=3):
        self.initial_capital = initial_capital
        self.target_capital = target_capital
        self.years_to_target = years_to_target
        self.annual_drawdown = annual_drawdown
        self.drawdown_start_year = drawdown_start_year
        
        # Calculate required annual return considering drawdowns
        self.required_annual_return = self.calculate_required_return()
        
        print(f"🏈 <PERSON>win's Make the World Better Foundation Portfolio")
        print(f"   💵 Starting Capital: ${initial_capital:,}")
        print(f"   🎯 Target Capital (10 years): ${target_capital:,}")
        print(f"   📉 Annual Drawdown: ${annual_drawdown:,} (starting year {drawdown_start_year})")
        print(f"   📈 Required Annual Return: {self.required_annual_return:.2%}")
    
    def calculate_required_return(self):
        """Calculate required annual return considering drawdowns"""
        total_drawdowns = self.annual_drawdown * (self.years_to_target - self.drawdown_start_year + 1)
        adjusted_target = self.target_capital + total_drawdowns
        required_return = (adjusted_target / self.initial_capital) ** (1/self.years_to_target) - 1
        return required_return
    
    def get_sample_tickers(self):
        """Get a sample of ESG-aligned tickers for quick testing"""
        growth_tickers = [
            "MSFT", "GOOGL", "AAPL", "JNJ", "UNH", "PFE", "NFLX", "TSLA",
            "NEE", "SO", "DUK", "EXC", "AWK", "WEC", "XEL", "AES"
        ]
        
        stable_tickers = [
            "AGG", "BND", "IGSB", "SHY", "TLT", "VYM", "SCHD", "VNQ",
            "TIPS", "PBW", "ICLN", "ESGU", "SUSB", "FLGR", "VGIT", "LQD"
        ]
        
        return growth_tickers, stable_tickers
    
    def fetch_data(self, tickers, max_tickers=20):
        """Fetch price data for tickers"""
        if len(tickers) > max_tickers:
            tickers = tickers[:max_tickers]
        
        print(f"   📥 Fetching data for {len(tickers)} tickers...")
        
        end_date = datetime.now().date()
        start_date = (datetime.now() - timedelta(days=365)).date()
        
        try:
            data = yf.download(tickers, start=start_date, end=end_date, progress=False)
            
            if data.empty:
                return pd.DataFrame()
            
            if 'Close' in data.columns:
                close_data = data['Close']
            elif isinstance(data.columns, pd.MultiIndex):
                close_data = data.xs('Close', level=1, axis=1)
            else:
                close_data = data
            
            # Remove tickers with insufficient data
            valid_tickers = []
            for ticker in tickers:
                if ticker in close_data.columns:
                    ticker_data = close_data[ticker].dropna()
                    if len(ticker_data) > 100:
                        valid_tickers.append(ticker)
            
            if valid_tickers:
                final_data = close_data[valid_tickers]
                print(f"   ✅ Successfully fetched {len(valid_tickers)} tickers")
                return final_data
            else:
                return pd.DataFrame()
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return pd.DataFrame()
    
    def optimize_portfolio(self, data, portfolio_type, max_weight=0.15):
        """Optimize portfolio using mean-variance optimization"""
        print(f"\n🎯 Optimizing {portfolio_type} Portfolio")
        
        if data.empty or len(data.columns) < 3:
            print(f"   ❌ Insufficient data for {portfolio_type}")
            return None, None
        
        returns = data.pct_change().dropna()
        mean_returns = returns.mean()
        cov_matrix = returns.cov()
        cov_matrix += np.eye(len(cov_matrix)) * 1e-6
        
        print(f"   📊 Assets: {len(mean_returns)}")
        print(f"   📈 Avg Daily Return: {mean_returns.mean():.6f}")
        print(f"   📊 Avg Daily Volatility: {returns.std().mean():.6f}")
        
        def neg_sharpe(weights):
            port_return = np.dot(weights, mean_returns) * 252
            port_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix * 252, weights)))
            if port_vol <= 0:
                return 999
            return -(port_return - 0.03) / port_vol
        
        n_assets = len(mean_returns)
        constraints = [{'type': 'eq', 'fun': lambda x: np.sum(x) - 1}]
        bounds = tuple((0, max_weight) for _ in range(n_assets))
        initial_weights = np.ones(n_assets) / n_assets
        
        try:
            result = minimize(neg_sharpe, initial_weights, method='SLSQP',
                            bounds=bounds, constraints=constraints,
                            options={'maxiter': 1000})
            
            if result.success:
                weights = result.x
                port_return = np.dot(weights, mean_returns) * 252
                port_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix * 252, weights)))
                sharpe = (port_return - 0.03) / port_vol
                
                print(f"   ✅ Optimization successful!")
                print(f"   📈 Expected Return: {port_return:.2%}")
                print(f"   📊 Volatility: {port_vol:.2%}")
                print(f"   ⚡ Sharpe Ratio: {sharpe:.3f}")
                
                return weights, {
                    'return': port_return,
                    'volatility': port_vol,
                    'sharpe': sharpe,
                    'tickers': data.columns.tolist()
                }
            else:
                print(f"   ❌ Optimization failed")
                return None, None
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return None, None

def main():
    """Main execution for Connor Barwin's MTWB Foundation Portfolio"""
    print("🏈 CONNOR BARWIN'S MAKE THE WORLD BETTER FOUNDATION PORTFOLIO")
    print("=" * 70)
    
    optimizer = MTWBPortfolioOptimizer()
    
    try:
        # Get sample tickers for quick testing
        growth_tickers, stable_tickers = optimizer.get_sample_tickers()
        
        print(f"\n📊 FETCHING MARKET DATA")
        print("=" * 50)
        
        growth_data = optimizer.fetch_data(growth_tickers, max_tickers=16)
        stable_data = optimizer.fetch_data(stable_tickers, max_tickers=16)
        
        if growth_data.empty or stable_data.empty:
            print("❌ Insufficient data for optimization")
            return None
        
        # Optimize each bucket
        growth_weights, growth_metrics = optimizer.optimize_portfolio(growth_data, "GROWTH-FOCUSED", max_weight=0.15)
        stable_weights, stable_metrics = optimizer.optimize_portfolio(stable_data, "STABLE INCOME", max_weight=0.20)
        
        if growth_weights is None or stable_weights is None:
            print("❌ Optimization failed")
            return None
        
        # Combine portfolios (60% growth, 40% stable)
        print(f"\n🎯 COMBINING MTWB ESG-ALIGNED PORTFOLIO")
        print("=" * 50)
        
        growth_allocation = 0.60
        stable_allocation = 0.40
        
        combined_return = (growth_metrics['return'] * growth_allocation + 
                          stable_metrics['return'] * stable_allocation)
        combined_vol = np.sqrt((growth_metrics['volatility'] * growth_allocation)**2 + 
                              (stable_metrics['volatility'] * stable_allocation)**2)
        combined_sharpe = (combined_return - 0.03) / combined_vol
        
        print(f"   📈 Combined Expected Return: {combined_return:.2%}")
        print(f"   📊 Combined Volatility: {combined_vol:.2%}")
        print(f"   ⚡ Combined Sharpe Ratio: {combined_sharpe:.3f}")
        
        # Show top holdings
        print(f"\n🏆 TOP HOLDINGS BY INVESTMENT CATEGORY:")
        print(f"\n📈 GROWTH-FOCUSED (60% = ${optimizer.initial_capital * 0.60:,.0f}):")
        growth_top = sorted(zip(growth_data.columns, growth_weights), key=lambda x: x[1], reverse=True)[:8]
        for i, (ticker, weight) in enumerate(growth_top, 1):
            allocation = weight * growth_allocation
            dollar_amount = allocation * optimizer.initial_capital
            print(f"   {i:2d}. {ticker:8s} | {allocation:6.2%} | ${dollar_amount:,.0f}")
        
        print(f"\n📊 STABLE INCOME (40% = ${optimizer.initial_capital * 0.40:,.0f}):")
        stable_top = sorted(zip(stable_data.columns, stable_weights), key=lambda x: x[1], reverse=True)[:8]
        for i, (ticker, weight) in enumerate(stable_top, 1):
            allocation = weight * stable_allocation
            dollar_amount = allocation * optimizer.initial_capital
            print(f"   {i:2d}. {ticker:8s} | {allocation:6.2%} | ${dollar_amount:,.0f}")
        
        print(f"\n🎉 MTWB ESG-ALIGNED PORTFOLIO OPTIMIZATION COMPLETE!")
        print(f"   💰 Starting Capital: ${optimizer.initial_capital:,}")
        print(f"   🎯 Target Capital (10 years): ${optimizer.target_capital:,}")
        print(f"   📈 Required Annual Return: {optimizer.required_annual_return:.2%}")
        print(f"   📈 Expected Annual Return: {combined_return:.2%}")
        print(f"   📊 Annual Volatility: {combined_vol:.2%}")
        print(f"   ⚡ Sharpe Ratio: {combined_sharpe:.3f}")
        
        # Check if portfolio meets MTWB requirements
        meets_target = combined_return >= optimizer.required_annual_return
        print(f"   {'✅' if meets_target else '❌'} Meets MTWB Target: {meets_target}")
        if not meets_target:
            shortfall = optimizer.required_annual_return - combined_return
            print(f"   📉 Return Shortfall: {shortfall:.2%} annually")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = main()
